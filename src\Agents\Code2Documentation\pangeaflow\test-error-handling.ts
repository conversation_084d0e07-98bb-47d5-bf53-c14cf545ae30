// Test script to verify error handling improvements in Code2Documentation PangeaFlow

import { executeCode2DocumentationWorkflow, createDefaultSharedStore } from './flow/pangeaFlow';
import { onError, onProgress, onStatus, onComplete, removeAllListeners } from './utils/events';

/**
 * Test error handling by creating a workflow with invalid parameters
 */
async function testErrorHandling() {
  console.log('🧪 Testing Code2Documentation PangeaFlow error handling...');

  // Set up event listeners to capture errors
  let errorCaught = false;
  let errorDetails: any = null;

  const errorListener = onError((event) => {
    console.log('✅ Error event received:', event);
    errorCaught = true;
    errorDetails = event;
  });

  const progressListener = onProgress((event) => {
    console.log('📊 Progress event:', event);
  });

  const statusListener = onStatus((event) => {
    console.log('📋 Status event:', event);
  });

  const completeListener = onComplete((event) => {
    console.log('🎉 Complete event:', event);
  });

  try {
    // Create invalid shared store to trigger errors
    const invalidShared = createDefaultSharedStore({
      user_id: 'test-user',
      repo_url: 'https://invalid-repo-url-that-does-not-exist.com/fake/repo',
      selected_files: ['nonexistent-file.js'],
      language: 'english',
      max_abstraction_num: 5
    });

    console.log('🔄 Executing workflow with invalid parameters...');
    const result = await executeCode2DocumentationWorkflow(invalidShared);

    console.log('📊 Workflow result:', result);

    // Check if error was properly handled
    if (!result.success && result.error) {
      console.log('✅ Workflow correctly returned error result');
      console.log('❌ Error message:', result.error.message);
    } else {
      console.log('❌ Expected workflow to fail but it succeeded');
    }

    // Wait a bit for events to propagate
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if error events were emitted
    if (errorCaught) {
      console.log('✅ Error events were properly emitted');
      console.log('📋 Error details:', errorDetails);
    } else {
      console.log('❌ No error events were emitted');
    }

  } catch (error) {
    console.log('✅ Workflow threw error as expected:', error);
  } finally {
    // Clean up event listeners
    errorListener();
    progressListener();
    statusListener();
    completeListener();
    removeAllListeners();
    
    console.log('🧹 Cleaned up event listeners');
  }

  console.log('🏁 Error handling test completed');
}

/**
 * Test successful workflow execution
 */
async function testSuccessfulExecution() {
  console.log('🧪 Testing successful Code2Documentation workflow...');

  // Set up event listeners
  const events: any[] = [];

  const errorListener = onError((event) => {
    console.log('❌ Unexpected error event:', event);
    events.push({ type: 'error', event });
  });

  const progressListener = onProgress((event) => {
    console.log('📊 Progress event:', event);
    events.push({ type: 'progress', event });
  });

  const statusListener = onStatus((event) => {
    console.log('📋 Status event:', event);
    events.push({ type: 'status', event });
  });

  const completeListener = onComplete((event) => {
    console.log('🎉 Complete event:', event);
    events.push({ type: 'complete', event });
  });

  try {
    // Create valid shared store (using a small test repository)
    const validShared = createDefaultSharedStore({
      user_id: 'test-user',
      repo_url: 'https://github.com/octocat/Hello-World', // Small test repo
      selected_files: ['README'], // Simple file
      language: 'english',
      max_abstraction_num: 2
    });

    console.log('🔄 Executing workflow with valid parameters...');
    const result = await executeCode2DocumentationWorkflow(validShared);

    console.log('📊 Workflow result:', result);

    if (result.success) {
      console.log('✅ Workflow completed successfully');
    } else {
      console.log('❌ Workflow failed:', result.error?.message);
    }

    // Wait for events to propagate
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('📋 Total events captured:', events.length);
    events.forEach((event, index) => {
      console.log(`  ${index + 1}. ${event.type}:`, event.event);
    });

  } catch (error) {
    console.log('❌ Unexpected error during successful test:', error);
  } finally {
    // Clean up event listeners
    errorListener();
    progressListener();
    statusListener();
    completeListener();
    removeAllListeners();
    
    console.log('🧹 Cleaned up event listeners');
  }

  console.log('🏁 Successful execution test completed');
}

// Export test functions for use in other contexts
export { testErrorHandling, testSuccessfulExecution };

// Run tests if this file is executed directly
if (require.main === module) {
  async function runTests() {
    console.log('🚀 Starting Code2Documentation error handling tests...\n');
    
    await testErrorHandling();
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Uncomment to test successful execution (requires network access)
    // await testSuccessfulExecution();
    
    console.log('✅ All tests completed!');
  }

  runTests().catch(console.error);
}
