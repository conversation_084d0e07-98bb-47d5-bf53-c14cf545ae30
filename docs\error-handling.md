# Error Handling

PangeaFlow provides comprehensive error handling capabilities with built-in recovery strategies, hierarchical error boundaries, and automatic retry mechanisms. This guide covers how to implement robust error handling in your workflows.

## Error Handling Philosophy

PangeaFlow follows a multi-layered error handling approach:

1. **Component-Level**: Each agent handles its own errors
2. **Workflow-Level**: Workflow-wide error handlers and recovery
3. **System-Level**: Global error boundaries and fallbacks

## Basic Error Handling

### Agent-Level Error Handling

Every agent should implement comprehensive error handling:

```typescript
class RobustAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      // Emit status update
      this.emit('agent.status', {
        agentName: 'RobustAgent',
        status: 'processing',
        message: 'Starting work'
      });
      
      // Perform work with telemetry
      const result = await this.withTelemetry('main-operation', async () => {
        return await this.performWork(context.metadata);
      });
      
      // Success case
      this.emit('agent.status', {
        agentName: 'RobustAgent',
        status: 'completed',
        message: 'Work completed successfully'
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result }
      };
      
    } catch (error) {
      // Emit error event with context
      this.emit('agent.error', {
        agentName: 'RobustAgent',
        error: error.message,
        stage: 'main-operation',
        retryable: this.isRetryableError(error),
        context: context.metadata
      });
      
      // Return error result
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: this.determineErrorAction(error),
        metadata: context.metadata
      };
    }
  }
  
  private isRetryableError(error: Error): boolean {
    // Determine if error is retryable
    const retryableErrors = [
      'ECONNRESET',
      'ETIMEDOUT',
      'ENOTFOUND',
      'Rate limit exceeded'
    ];
    
    return retryableErrors.some(retryableError => 
      error.message.includes(retryableError)
    );
  }
  
  private determineErrorAction(error: Error): string[] {
    if (this.isRetryableError(error)) {
      return ['retry'];
    } else {
      return ['error'];
    }
  }
}
```

## Retry Mechanisms

### Automatic Retry with Exponential Backoff

```typescript
class RetryableAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.performWork(context.metadata);
      
      // Reset retry counter on success
      this.currentRetry = 0;
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result }
      };
      
    } catch (error) {
      if (this.shouldRetry(error)) {
        this.currentRetry++;
        
        // Calculate exponential backoff delay
        const delay = Math.pow(2, this.currentRetry) * 1000; // 2^n seconds
        
        this.emit('agent.retry', {
          agentName: 'RetryableAgent',
          attempt: this.currentRetry,
          maxAttempts: this.maxRetries,
          delay,
          error: error.message
        });
        
        // Schedule retry
        setTimeout(() => {
          // In real implementation, you'd trigger the retry through the workflow
          this.emit('retry.scheduled', {
            agentId: this.id,
            attempt: this.currentRetry,
            delay
          });
        }, delay);
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['retry'],
          metadata: { 
            ...context.metadata, 
            retryAttempt: this.currentRetry,
            retryDelay: delay
          }
        };
      } else {
        // Max retries exceeded or non-retryable error
        this.currentRetry = 0;
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          metadata: context.metadata
        };
      }
    }
  }
  
  private shouldRetry(error: Error): boolean {
    return this.currentRetry < this.maxRetries && this.isRetryableError(error);
  }
  
  private isRetryableError(error: Error): boolean {
    // Define retryable error conditions
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /rate limit/i,
      /temporary/i,
      /503/,
      /502/,
      /429/
    ];
    
    return retryablePatterns.some(pattern => pattern.test(error.message));
  }
}
```

### Circuit Breaker Pattern

```typescript
class CircuitBreakerAgent extends AgentComponent {
  private failureCount = 0;
  private lastFailureTime = 0;
  private circuitState: 'closed' | 'open' | 'half-open' = 'closed';
  private failureThreshold = 5;
  private recoveryTimeout = 60000; // 1 minute
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // Check circuit breaker state
    if (this.circuitState === 'open') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.circuitState = 'half-open';
        this.emit('circuit.half-open', {
          agentId: this.id,
          failureCount: this.failureCount
        });
      } else {
        // Circuit is open, fail fast
        this.emit('circuit.open', {
          agentId: this.id,
          message: 'Circuit breaker is open, failing fast'
        });
        
        return {
          success: false,
          error: new Error('Circuit breaker is open'),
          events: [],
          nextActions: ['fallback'],
          metadata: context.metadata
        };
      }
    }
    
    try {
      const result = await this.performWork(context.metadata);
      
      // Success - reset circuit breaker
      if (this.circuitState === 'half-open') {
        this.circuitState = 'closed';
        this.failureCount = 0;
        this.emit('circuit.closed', {
          agentId: this.id,
          message: 'Circuit breaker reset to closed'
        });
      }
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result }
      };
      
    } catch (error) {
      this.failureCount++;
      this.lastFailureTime = Date.now();
      
      // Check if we should open the circuit
      if (this.failureCount >= this.failureThreshold) {
        this.circuitState = 'open';
        this.emit('circuit.opened', {
          agentId: this.id,
          failureCount: this.failureCount,
          error: error.message
        });
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['fallback'],
        metadata: context.metadata
      };
    }
  }
}
```

## Fallback Strategies

### Graceful Degradation

```typescript
class GracefulDegradationAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      // Try primary operation
      const result = await this.performPrimaryOperation(context.metadata);
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result, mode: 'primary' }
      };
      
    } catch (primaryError) {
      this.emit('primary.failed', {
        agentName: 'GracefulDegradationAgent',
        error: primaryError.message,
        attempting: 'fallback'
      });
      
      try {
        // Try fallback operation
        const fallbackResult = await this.performFallbackOperation(context.metadata);
        
        this.emit('fallback.succeeded', {
          agentName: 'GracefulDegradationAgent',
          message: 'Fallback operation successful'
        });
        
        return {
          success: true,
          data: fallbackResult,
          events: [],
          nextActions: ['continue'],
          metadata: { 
            ...context.metadata, 
            result: fallbackResult, 
            mode: 'fallback',
            primaryError: primaryError.message
          }
        };
        
      } catch (fallbackError) {
        // Both primary and fallback failed
        this.emit('complete.failure', {
          agentName: 'GracefulDegradationAgent',
          primaryError: primaryError.message,
          fallbackError: fallbackError.message
        });
        
        return {
          success: false,
          error: new Error(`Primary and fallback failed: ${primaryError.message}, ${fallbackError.message}`),
          events: [],
          nextActions: ['error'],
          metadata: context.metadata
        };
      }
    }
  }
  
  private async performPrimaryOperation(metadata: any) {
    // High-quality but potentially unreliable operation
    return await this.callPremiumAPI(metadata);
  }
  
  private async performFallbackOperation(metadata: any) {
    // Lower-quality but more reliable operation
    return await this.callBasicAPI(metadata);
  }
}
```

### Multi-Level Fallback Chain

```typescript
class FallbackChainAgent extends AgentComponent {
  private fallbackStrategies = [
    { name: 'premium', method: this.callPremiumService },
    { name: 'standard', method: this.callStandardService },
    { name: 'basic', method: this.callBasicService },
    { name: 'cache', method: this.getCachedResult },
    { name: 'default', method: this.getDefaultResult }
  ];
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    let lastError: Error;
    
    for (const strategy of this.fallbackStrategies) {
      try {
        this.emit('strategy.attempting', {
          agentName: 'FallbackChainAgent',
          strategy: strategy.name
        });
        
        const result = await strategy.method.call(this, context.metadata);
        
        this.emit('strategy.succeeded', {
          agentName: 'FallbackChainAgent',
          strategy: strategy.name
        });
        
        return {
          success: true,
          data: result,
          events: [],
          nextActions: ['continue'],
          metadata: { 
            ...context.metadata, 
            result, 
            usedStrategy: strategy.name 
          }
        };
        
      } catch (error) {
        lastError = error as Error;
        
        this.emit('strategy.failed', {
          agentName: 'FallbackChainAgent',
          strategy: strategy.name,
          error: error.message
        });
        
        // Continue to next strategy
      }
    }
    
    // All strategies failed
    return {
      success: false,
      error: lastError!,
      events: [],
      nextActions: ['error'],
      metadata: context.metadata
    };
  }
}
```

## Workflow-Level Error Handling

### Error Coordinator

```typescript
class ErrorCoordinatorAgent extends AgentComponent {
  private errorCounts = new Map<string, number>();
  private errorThreshold = 3;
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('error-coordinator', eventBus, telemetry);
    
    // Listen to all agent errors
    this.eventBus.on('agent.error', this.handleAgentError.bind(this));
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { errorSource, errorType, errorMessage } = context.metadata;
    
    // Analyze error and determine recovery strategy
    const recoveryStrategy = this.determineRecoveryStrategy(
      errorSource as string,
      errorType as string,
      errorMessage as string
    );
    
    this.emit('error.recovery.planned', {
      errorSource,
      errorType,
      strategy: recoveryStrategy.name,
      actions: recoveryStrategy.actions
    });
    
    return {
      success: true,
      data: recoveryStrategy,
      events: [],
      nextActions: recoveryStrategy.actions,
      metadata: { ...context.metadata, recoveryStrategy }
    };
  }
  
  private async handleAgentError(event: any) {
    const { agentName, error, retryable } = event.payload;
    
    // Track error frequency
    const errorKey = `${agentName}-${error}`;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);
    
    // Check if error frequency exceeds threshold
    if (currentCount + 1 >= this.errorThreshold) {
      this.emit('error.pattern.detected', {
        agentName,
        error,
        frequency: currentCount + 1,
        recommendation: 'Consider disabling or replacing this component'
      });
    }
  }
  
  private determineRecoveryStrategy(source: string, type: string, message: string) {
    // Network-related errors
    if (message.includes('network') || message.includes('timeout')) {
      return {
        name: 'network-retry',
        actions: ['wait', 'retry'],
        delay: 5000
      };
    }
    
    // Rate limiting errors
    if (message.includes('rate limit')) {
      return {
        name: 'rate-limit-backoff',
        actions: ['wait', 'retry'],
        delay: 60000
      };
    }
    
    // Authentication errors
    if (message.includes('auth') || message.includes('unauthorized')) {
      return {
        name: 'auth-refresh',
        actions: ['refresh-auth', 'retry'],
        delay: 1000
      };
    }
    
    // Default strategy
    return {
      name: 'fallback',
      actions: ['fallback'],
      delay: 0
    };
  }
}
```

### Global Error Boundary

```typescript
class GlobalErrorBoundary {
  private criticalErrors = 0;
  private maxCriticalErrors = 5;
  private shutdownInitiated = false;
  
  constructor(private workflow: WorkflowOrchestrator) {
    this.setupErrorHandling();
  }
  
  private setupErrorHandling() {
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.handleCriticalError('uncaughtException', error);
    });
    
    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      this.handleCriticalError('unhandledRejection', new Error(String(reason)));
    });
    
    // Handle workflow-level errors
    this.workflow.on('workflow.failed', (event) => {
      this.handleWorkflowError(event);
    });
  }
  
  private handleCriticalError(type: string, error: Error) {
    this.criticalErrors++;
    
    console.error(`Critical Error [${type}]:`, {
      error: error.message,
      stack: error.stack,
      criticalErrorCount: this.criticalErrors,
      timestamp: new Date().toISOString()
    });
    
    // Emit critical error event
    this.workflow.emit('system.critical.error', {
      type,
      error: error.message,
      stack: error.stack,
      criticalErrorCount: this.criticalErrors
    });
    
    // Check if we should initiate shutdown
    if (this.criticalErrors >= this.maxCriticalErrors && !this.shutdownInitiated) {
      this.initiateGracefulShutdown();
    }
  }
  
  private handleWorkflowError(event: any) {
    const { error, workflowId } = event.payload;
    
    console.error(`Workflow Error [${workflowId}]:`, {
      error: error.message,
      timestamp: new Date().toISOString()
    });
    
    // Attempt workflow recovery
    this.attemptWorkflowRecovery(workflowId, error);
  }
  
  private async attemptWorkflowRecovery(workflowId: string, error: Error) {
    try {
      // Save current state
      await this.saveWorkflowState(workflowId);
      
      // Attempt to restart workflow from last known good state
      this.workflow.emit('workflow.recovery.initiated', {
        workflowId,
        error: error.message,
        recoveryStrategy: 'restart-from-checkpoint'
      });
      
    } catch (recoveryError) {
      console.error('Workflow recovery failed:', recoveryError);
      
      this.workflow.emit('workflow.recovery.failed', {
        workflowId,
        originalError: error.message,
        recoveryError: recoveryError.message
      });
    }
  }
  
  private async saveWorkflowState(workflowId: string) {
    // Save workflow state for recovery
    const state = {
      workflowId,
      timestamp: Date.now(),
      metrics: this.workflow.getMetrics(),
      // Add other relevant state information
    };
    
    // Save to persistent storage
    await this.persistState(state);
  }
  
  private initiateGracefulShutdown() {
    this.shutdownInitiated = true;
    
    console.error('Initiating graceful shutdown due to critical errors');
    
    // Emit shutdown event
    this.workflow.emit('system.shutdown.initiated', {
      reason: 'critical-errors',
      criticalErrorCount: this.criticalErrors,
      timestamp: Date.now()
    });
    
    // Perform cleanup
    setTimeout(() => {
      process.exit(1);
    }, 5000); // Give 5 seconds for cleanup
  }
}
```

## Error Recovery Patterns

### Checkpoint and Rollback

```typescript
class CheckpointAgent extends AgentComponent {
  private checkpoints = new Map<string, any>();
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const checkpointId = `checkpoint-${Date.now()}`;
    
    try {
      // Create checkpoint before risky operation
      await this.createCheckpoint(checkpointId, context.metadata);
      
      // Perform risky operation
      const result = await this.performRiskyOperation(context.metadata);
      
      // Success - remove checkpoint
      this.removeCheckpoint(checkpointId);
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result }
      };
      
    } catch (error) {
      // Rollback to checkpoint
      const checkpointData = await this.rollbackToCheckpoint(checkpointId);
      
      this.emit('checkpoint.rollback', {
        agentName: 'CheckpointAgent',
        checkpointId,
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['retry'],
        metadata: checkpointData
      };
    }
  }
  
  private async createCheckpoint(id: string, data: any) {
    this.checkpoints.set(id, JSON.parse(JSON.stringify(data)));
    
    this.emit('checkpoint.created', {
      checkpointId: id,
      timestamp: Date.now()
    });
  }
  
  private async rollbackToCheckpoint(id: string) {
    const checkpointData = this.checkpoints.get(id);
    if (!checkpointData) {
      throw new Error(`Checkpoint ${id} not found`);
    }
    
    return checkpointData;
  }
  
  private removeCheckpoint(id: string) {
    this.checkpoints.delete(id);
  }
}
```

## Best Practices

### 1. Error Classification

```typescript
enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

enum ErrorCategory {
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system'
}

class ErrorClassifier {
  static classify(error: Error): { severity: ErrorSeverity; category: ErrorCategory } {
    const message = error.message.toLowerCase();
    
    // Network errors
    if (message.includes('network') || message.includes('timeout')) {
      return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.NETWORK };
    }
    
    // Authentication errors
    if (message.includes('auth') || message.includes('unauthorized')) {
      return { severity: ErrorSeverity.HIGH, category: ErrorCategory.AUTHENTICATION };
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('invalid')) {
      return { severity: ErrorSeverity.LOW, category: ErrorCategory.VALIDATION };
    }
    
    // Default
    return { severity: ErrorSeverity.MEDIUM, category: ErrorCategory.SYSTEM };
  }
}
```

### 2. Structured Error Reporting

```typescript
interface ErrorReport {
  id: string;
  timestamp: number;
  severity: ErrorSeverity;
  category: ErrorCategory;
  component: string;
  operation: string;
  message: string;
  stack?: string;
  context: Record<string, unknown>;
  retryable: boolean;
  recoveryActions: string[];
}

class ErrorReporter {
  static createReport(
    error: Error,
    component: string,
    operation: string,
    context: Record<string, unknown>
  ): ErrorReport {
    const classification = ErrorClassifier.classify(error);
    
    return {
      id: crypto.randomUUID(),
      timestamp: Date.now(),
      severity: classification.severity,
      category: classification.category,
      component,
      operation,
      message: error.message,
      stack: error.stack,
      context,
      retryable: this.isRetryable(error),
      recoveryActions: this.getRecoveryActions(classification)
    };
  }
  
  private static isRetryable(error: Error): boolean {
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /rate limit/i,
      /temporary/i
    ];
    
    return retryablePatterns.some(pattern => pattern.test(error.message));
  }
  
  private static getRecoveryActions(classification: { severity: ErrorSeverity; category: ErrorCategory }): string[] {
    switch (classification.category) {
      case ErrorCategory.NETWORK:
        return ['retry', 'fallback'];
      case ErrorCategory.AUTHENTICATION:
        return ['refresh-auth', 'retry'];
      case ErrorCategory.VALIDATION:
        return ['validate-input', 'sanitize'];
      default:
        return ['retry', 'fallback'];
    }
  }
}
```

## Next Steps

- [Telemetry & Monitoring](./telemetry-monitoring.md) - Monitor error patterns and recovery
- [Examples](./examples.md) - See error handling in action
- [Troubleshooting](./troubleshooting.md) - Debug common error scenarios
