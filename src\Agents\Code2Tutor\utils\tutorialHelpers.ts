// src/Agents/Code2Tutor/utils/tutorialHelpers.ts

import { LearningConcept, TutorialSection, Exercise, CodeExample } from '../types';

/**
 * Utility functions for tutorial creation and management
 */

/**
 * Calculate estimated reading time for tutorial content
 */
export function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200; // Average reading speed
  const words = content.split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

/**
 * Calculate estimated completion time including exercises
 */
export function calculateCompletionTime(sections: TutorialSection[]): number {
  let totalTime = 0;

  sections.forEach(section => {
    // Reading time
    totalTime += calculateReadingTime(section.content);
    
    // Exercise time
    section.exercises.forEach(exercise => {
      switch (exercise.difficulty) {
        case 'easy':
          totalTime += 5;
          break;
        case 'medium':
          totalTime += 10;
          break;
        case 'hard':
          totalTime += 20;
          break;
      }
    });

    // Code example review time
    totalTime += section.codeExamples.length * 3;
  });

  return totalTime;
}

/**
 * Validate concept dependencies and suggest optimal ordering
 */
export function validateConceptDependencies(concepts: LearningConcept[]): {
  isValid: boolean;
  issues: string[];
  suggestedOrder: string[];
} {
  const issues: string[] = [];
  const conceptNames = concepts.map(c => c.name);
  
  // Check for missing prerequisites
  concepts.forEach(concept => {
    concept.prerequisites.forEach(prereq => {
      if (!conceptNames.includes(prereq)) {
        issues.push(`Concept "${concept.name}" requires "${prereq}" which is not defined`);
      }
    });
  });

  // Check for circular dependencies
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  function hasCycle(conceptName: string): boolean {
    if (recursionStack.has(conceptName)) {
      issues.push(`Circular dependency detected involving "${conceptName}"`);
      return true;
    }
    
    if (visited.has(conceptName)) {
      return false;
    }
    
    visited.add(conceptName);
    recursionStack.add(conceptName);
    
    const concept = concepts.find(c => c.name === conceptName);
    if (concept) {
      for (const prereq of concept.prerequisites) {
        if (hasCycle(prereq)) {
          return true;
        }
      }
    }
    
    recursionStack.delete(conceptName);
    return false;
  }

  concepts.forEach(concept => {
    if (!visited.has(concept.name)) {
      hasCycle(concept.name);
    }
  });

  // Generate suggested order using topological sort
  const suggestedOrder = topologicalSort(concepts);

  return {
    isValid: issues.length === 0,
    issues,
    suggestedOrder
  };
}

/**
 * Topological sort for concept ordering
 */
function topologicalSort(concepts: LearningConcept[]): string[] {
  const graph = new Map<string, string[]>();
  const inDegree = new Map<string, number>();
  
  // Initialize graph
  concepts.forEach(concept => {
    graph.set(concept.name, concept.prerequisites);
    inDegree.set(concept.name, concept.prerequisites.length);
  });

  const queue: string[] = [];
  const result: string[] = [];

  // Find concepts with no prerequisites
  inDegree.forEach((degree, concept) => {
    if (degree === 0) {
      queue.push(concept);
    }
  });

  while (queue.length > 0) {
    const current = queue.shift()!;
    result.push(current);

    // Update in-degrees of dependent concepts
    concepts.forEach(concept => {
      if (concept.prerequisites.includes(current)) {
        const newDegree = inDegree.get(concept.name)! - 1;
        inDegree.set(concept.name, newDegree);
        
        if (newDegree === 0) {
          queue.push(concept.name);
        }
      }
    });
  }

  return result;
}

/**
 * Generate learning objectives from concepts
 */
export function generateLearningObjectives(concepts: LearningConcept[]): string[] {
  const objectives: string[] = [];

  concepts.forEach(concept => {
    switch (concept.difficulty) {
      case 'beginner':
        objectives.push(`Understand the basics of ${concept.name}`);
        break;
      case 'intermediate':
        objectives.push(`Apply ${concept.name} in practical scenarios`);
        break;
      case 'advanced':
        objectives.push(`Master advanced ${concept.name} techniques`);
        break;
    }
  });

  // Add general objectives
  objectives.push('Build hands-on experience through practical exercises');
  objectives.push('Develop problem-solving skills in real-world contexts');

  return objectives;
}

/**
 * Assess tutorial difficulty based on concepts
 */
export function assessTutorialDifficulty(concepts: LearningConcept[]): 'beginner' | 'intermediate' | 'advanced' {
  const difficultyCounts = {
    beginner: 0,
    intermediate: 0,
    advanced: 0
  };

  concepts.forEach(concept => {
    difficultyCounts[concept.difficulty]++;
  });

  // Determine overall difficulty
  if (difficultyCounts.advanced > concepts.length * 0.3) {
    return 'advanced';
  } else if (difficultyCounts.intermediate > concepts.length * 0.5) {
    return 'intermediate';
  } else {
    return 'beginner';
  }
}

/**
 * Generate table of contents from sections
 */
export function generateTableOfContents(sections: TutorialSection[]): string {
  let toc = '## Table of Contents\n\n';
  
  sections.forEach((section, index) => {
    const sectionNumber = index + 1;
    const anchor = section.title.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '');
    toc += `${sectionNumber}. [${section.title}](#${anchor})\n`;
  });

  return toc;
}

/**
 * Validate exercise structure
 */
export function validateExercise(exercise: Exercise): string[] {
  const issues: string[] = [];

  if (!exercise.title || exercise.title.trim().length === 0) {
    issues.push('Exercise title is required');
  }

  if (!exercise.description || exercise.description.trim().length === 0) {
    issues.push('Exercise description is required');
  }

  if (!['coding', 'quiz', 'explanation', 'debugging'].includes(exercise.type)) {
    issues.push('Exercise type must be coding, quiz, explanation, or debugging');
  }

  if (!['easy', 'medium', 'hard'].includes(exercise.difficulty)) {
    issues.push('Exercise difficulty must be easy, medium, or hard');
  }

  if (exercise.hints.length === 0) {
    issues.push('Exercise should have at least one hint');
  }

  return issues;
}

/**
 * Generate progress indicators for tutorial sections
 */
export function generateProgressIndicators(sections: TutorialSection[], currentSection: number): string {
  const totalSections = sections.length;
  const progressPercentage = Math.round((currentSection / totalSections) * 100);
  
  let progressBar = '**Progress:** ';
  progressBar += `${currentSection}/${totalSections} sections completed (${progressPercentage}%)\n\n`;
  progressBar += '[';
  
  for (let i = 0; i < totalSections; i++) {
    if (i < currentSection) {
      progressBar += '█';
    } else if (i === currentSection) {
      progressBar += '▓';
    } else {
      progressBar += '░';
    }
  }
  
  progressBar += ']\n\n';
  return progressBar;
}

/**
 * Extract code snippets from content
 */
export function extractCodeSnippets(content: string): CodeExample[] {
  const codeBlocks: CodeExample[] = [];
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)\n```/g;
  let match;
  let index = 0;

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const language = match[1] || 'text';
    const code = match[2];
    
    codeBlocks.push({
      id: `snippet_${index}`,
      title: `Code Example ${index + 1}`,
      code,
      language,
      explanation: 'Code snippet extracted from tutorial content',
      runnable: ['javascript', 'python', 'java'].includes(language.toLowerCase()),
      expectedOutput: undefined
    });
    
    index++;
  }

  return codeBlocks;
}

/**
 * Format tutorial metadata for display
 */
export function formatTutorialMetadata(metadata: any): string {
  return `
**Tutorial Information**
- **Title:** ${metadata.title}
- **Target Audience:** ${metadata.targetAudience}
- **Estimated Time:** ${metadata.estimatedTime} minutes
- **Prerequisites:** ${metadata.prerequisites.join(', ') || 'None'}

**Learning Objectives:**
${metadata.learningObjectives.map((obj: string) => `- ${obj}`).join('\n')}
  `.trim();
}
