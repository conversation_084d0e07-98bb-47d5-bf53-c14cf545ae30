// src/Agents/Code2Documentation/pangeaflow/utils/telemetry.ts

export class TelemetryCollector {
  private metrics: Map<string, any[]> = new Map();
  private timers: Map<string, number> = new Map();
  
  // Start timing an operation
  startTimer(operation: string): void {
    this.timers.set(operation, Date.now());
  }
  
  // End timing an operation and record the duration
  endTimer(operation: string): number {
    const startTime = this.timers.get(operation);
    if (!startTime) {
      console.warn(`No timer found for operation: ${operation}`);
      return 0;
    }
    
    const duration = Date.now() - startTime;
    this.recordMetric(operation, { duration });
    this.timers.delete(operation);
    
    return duration;
  }
  
  // Record a metric
  recordMetric(name: string, value: any): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const metrics = this.metrics.get(name)!;
    metrics.push({
      ...value,
      timestamp: Date.now()
    });
  }
  
  // Get all metrics
  getMetrics(): Record<string, any[]> {
    const result: Record<string, any[]> = {};
    
    for (const [key, value] of this.metrics.entries()) {
      result[key] = value;
    }
    
    return result;
  }
  
  // Get metrics for a specific operation
  getMetricsForOperation(operation: string): any[] {
    return this.metrics.get(operation) || [];
  }
  
  // Clear all metrics
  clearMetrics(): void {
    this.metrics.clear();
    this.timers.clear();
  }
}
