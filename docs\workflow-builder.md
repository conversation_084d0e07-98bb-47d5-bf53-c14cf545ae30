# Workflow Builder

The WorkflowBuilder is PangeaFlow's fluent API for constructing workflows. It provides a clean, chainable interface for adding components and defining routes between them.

## Basic Usage

### Creating a Builder

```typescript
import { WorkflowBuilder } from '@/pangeaflow';

const builder = WorkflowBuilder.create();
```

### Builder Pattern

The WorkflowBuilder uses a fluent interface that allows method chaining:

```typescript
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .addMemoryAgent('storage')
  .route('start', 'planner')
  .route('execute', 'executor')
  .route('store', 'storage')
  .build();
```

## Adding Components

### ReasoningAgent

Add agents that use LLMs for reasoning and decision-making:

```typescript
const llmProvider = async (prompt: string, context: Record<string, unknown>) => {
  // Your LLM integration
  return await callLLM(prompt, context);
};

builder.addReasoningAgent('analyzer', llmProvider);
```

**Use Cases:**
- Planning and decision-making
- Content generation
- Analysis and reasoning
- Natural language processing

### ToolAgent

Add agents that execute tools and functions:

```typescript
const tools = {
  'file-reader': async (filePath: string) => {
    return await fs.readFile(filePath, 'utf-8');
  },
  'api-caller': async (url: string) => {
    return await fetch(url).then(r => r.json());
  },
  'data-processor': async (data: any[]) => {
    return data.map(item => processItem(item));
  }
};

builder.addToolAgent('file-processor', tools);
```

**Use Cases:**
- File operations
- API calls
- Data processing
- External system integration

### MemoryAgent

Add agents for state management and data storage:

```typescript
builder.addMemoryAgent('cache');
builder.addMemoryAgent('results-store');
```

**Use Cases:**
- Caching intermediate results
- Storing workflow state
- Managing shared data
- Result aggregation

## Routing System

### Basic Routing

Define how actions flow between components:

```typescript
builder
  .route('start', 'component1')           // Single target
  .route('process', 'component2')         // Sequential flow
  .route('complete', 'storage');          // End with storage
```

### Parallel Routing

Route to multiple components simultaneously:

```typescript
builder
  .route('analyze', 'analyzer1', 'analyzer2', 'analyzer3')  // Parallel execution
  .route('combine', 'aggregator');                         // Combine results
```

### Error Routing

Always define error handling routes:

```typescript
builder
  .route('start', 'main-processor')
  .route('retry', 'main-processor')       // Retry logic
  .route('error', 'error-handler')        // Error handling
  .route('fallback', 'fallback-processor'); // Fallback logic
```

## Advanced Patterns

### Sequential Processing

Create linear workflows for step-by-step processing:

```typescript
const sequential = WorkflowBuilder.create()
  .addReasoningAgent('step1', llmProvider)
  .addReasoningAgent('step2', llmProvider)
  .addReasoningAgent('step3', llmProvider)
  .addMemoryAgent('storage')
  .route('start', 'step1')
  .route('next1', 'step2')
  .route('next2', 'step3')
  .route('complete', 'storage')
  .route('error', 'step1')  // Restart on error
  .build();
```

### Fan-Out/Fan-In Pattern

Distribute work across multiple components, then aggregate:

```typescript
const fanOutIn = WorkflowBuilder.create()
  .addReasoningAgent('distributor', llmProvider)
  .addToolAgent('worker1', tools)
  .addToolAgent('worker2', tools)
  .addToolAgent('worker3', tools)
  .addReasoningAgent('aggregator', llmProvider)
  .addMemoryAgent('results')
  
  // Fan-out: distribute work
  .route('start', 'distributor')
  .route('distribute', 'worker1', 'worker2', 'worker3')
  
  // Fan-in: aggregate results
  .route('aggregate', 'aggregator')
  .route('store', 'results')
  .build();
```

### Conditional Branching

Use reasoning agents to make routing decisions:

```typescript
const conditional = WorkflowBuilder.create()
  .addReasoningAgent('decision-maker', llmProvider)
  .addToolAgent('path-a', toolsA)
  .addToolAgent('path-b', toolsB)
  .addMemoryAgent('storage')
  
  .route('start', 'decision-maker')
  .route('path-a', 'path-a')      // Conditional route A
  .route('path-b', 'path-b')      // Conditional route B
  .route('complete', 'storage')
  .build();
```

### Error Recovery Patterns

#### Retry with Backoff

```typescript
const retryPattern = WorkflowBuilder.create()
  .addReasoningAgent('main-task', llmProvider)
  .addReasoningAgent('retry-handler', llmProvider)
  .addMemoryAgent('state')
  
  .route('start', 'main-task')
  .route('success', 'state')
  .route('error', 'retry-handler')
  .route('retry', 'main-task')
  .route('give-up', 'state')
  .build();
```

#### Fallback Chain

```typescript
const fallbackPattern = WorkflowBuilder.create()
  .addReasoningAgent('primary', primaryLLM)
  .addReasoningAgent('secondary', secondaryLLM)
  .addToolAgent('basic-fallback', basicTools)
  .addMemoryAgent('results')
  
  .route('start', 'primary')
  .route('fallback-1', 'secondary')
  .route('fallback-2', 'basic-fallback')
  .route('complete', 'results')
  .build();
```

## Real-World Examples

### Code Analysis Workflow

```typescript
const codeAnalysis = WorkflowBuilder.create()
  // Repository analysis
  .addToolAgent('repo-fetcher', {
    'fetch-files': fetchGitHubFiles,
    'analyze-structure': analyzeRepoStructure
  })
  
  // Concept extraction
  .addReasoningAgent('concept-extractor', llmProvider)
  
  // Content generation
  .addReasoningAgent('content-generator', llmProvider)
  
  // Result storage
  .addMemoryAgent('tutorial-storage')
  
  // Define the flow
  .route('start', 'repo-fetcher')
  .route('extract-concepts', 'concept-extractor')
  .route('generate-content', 'content-generator')
  .route('store-tutorial', 'tutorial-storage')
  .route('error', 'concept-extractor')  // Retry from concept extraction
  .build();
```

### Data Processing Pipeline

```typescript
const dataPipeline = WorkflowBuilder.create()
  // Data ingestion
  .addToolAgent('data-ingester', {
    'fetch-data': fetchFromAPI,
    'validate-data': validateDataFormat
  })
  
  // Parallel processing
  .addToolAgent('processor-1', { 'clean-data': cleanData })
  .addToolAgent('processor-2', { 'enrich-data': enrichData })
  .addToolAgent('processor-3', { 'transform-data': transformData })
  
  // Analysis
  .addReasoningAgent('analyzer', llmProvider)
  
  // Storage
  .addMemoryAgent('data-warehouse')
  
  // Define the pipeline
  .route('start', 'data-ingester')
  .route('process', 'processor-1', 'processor-2', 'processor-3')
  .route('analyze', 'analyzer')
  .route('store', 'data-warehouse')
  .route('error', 'data-ingester')  // Restart pipeline on error
  .build();
```

## Best Practices

### Component Naming

Use descriptive, action-oriented names:

```typescript
// Good
.addReasoningAgent('code-analyzer', llmProvider)
.addToolAgent('file-processor', tools)
.addMemoryAgent('results-cache')

// Avoid
.addReasoningAgent('agent1', llmProvider)
.addToolAgent('tool', tools)
.addMemoryAgent('memory')
```

### Route Organization

Group related routes together:

```typescript
const workflow = WorkflowBuilder.create()
  // Add all components first
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .addMemoryAgent('storage')
  
  // Then define all routes
  .route('start', 'planner')
  .route('execute', 'executor')
  .route('store', 'storage')
  
  // Error handling routes last
  .route('error', 'planner')
  .route('retry', 'executor')
  .build();
```

### Error Handling

Always include comprehensive error handling:

```typescript
builder
  // Normal flow
  .route('start', 'main-component')
  .route('process', 'processor')
  .route('complete', 'storage')
  
  // Error handling
  .route('error', 'error-handler')
  .route('retry', 'main-component')
  .route('fallback', 'fallback-component')
  .route('abort', 'storage');  // Store partial results
```

### Testing Workflows

Build workflows that are easy to test:

```typescript
// Create a testable workflow factory
function createAnalysisWorkflow(llmProvider: LLMProvider, tools: Tools) {
  return WorkflowBuilder.create()
    .addReasoningAgent('analyzer', llmProvider)
    .addToolAgent('processor', tools)
    .addMemoryAgent('storage')
    .route('start', 'analyzer')
    .route('process', 'processor')
    .route('store', 'storage')
    .build();
}

// Easy to test with mocks
const mockLLM = async (prompt: string) => 'mock response';
const mockTools = { 'process': async (data: any) => data };
const testWorkflow = createAnalysisWorkflow(mockLLM, mockTools);
```

## Next Steps

- [Built-in Agents](./built-in-agents.md) - Learn about ReasoningAgent, ToolAgent, and MemoryAgent
- [Custom Agents](./custom-agents.md) - Create your own specialized agents
- [Examples](./examples.md) - See complete workflow examples
