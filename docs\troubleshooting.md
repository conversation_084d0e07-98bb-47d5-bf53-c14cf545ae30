# Troubleshooting

Common issues and solutions when working with PangeaFlow workflows.

## Common Issues

### 1. Workflow Execution Hangs

**Symptoms:**
- Workflow starts but never completes
- No error messages or events
- Process appears to be stuck

**Possible Causes:**
- Missing route definitions
- Circular routing
- Agent not returning proper ExecutionResult
- Event listener blocking execution

**Solutions:**

#### Check Route Definitions
```typescript
// Ensure all actions have corresponding routes
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .route('start', 'planner')
  .route('execute', 'executor')  // Make sure this route exists
  .route('complete', 'storage')  // And this one
  .build();
```

#### Verify Agent Return Values
```typescript
class MyAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.doWork();
      
      // Always return a proper ExecutionResult
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'], // Ensure this action has a route
        metadata: {}
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'], // Ensure error route exists
        metadata: {}
      };
    }
  }
}
```

#### Add Debugging
```typescript
// Add comprehensive event logging
workflow.on('step.started', (event) => {
  console.log('Step started:', event.source, event.payload);
});

workflow.on('step.completed', (event) => {
  console.log('Step completed:', event.source, event.payload);
});

workflow.on('step.failed', (event) => {
  console.error('Step failed:', event.source, event.payload);
});
```

### 2. Events Not Being Received

**Symptoms:**
- Event listeners not triggering
- Missing status updates
- Components not communicating

**Possible Causes:**
- Incorrect event type names
- Event listeners registered after events are emitted
- Event bus not properly shared between components

**Solutions:**

#### Verify Event Type Names
```typescript
// Use consistent event type names
const EventTypes = {
  AGENT_STATUS: 'agent.status',
  DATA_PROCESSED: 'data.processed',
  WORKFLOW_COMPLETE: 'workflow.completed'
};

// Emit events with correct types
this.emit(EventTypes.AGENT_STATUS, {
  agentName: 'MyAgent',
  status: 'processing'
});

// Listen with correct types
workflow.on(EventTypes.AGENT_STATUS, (event) => {
  console.log('Status update:', event.payload);
});
```

#### Register Listeners Early
```typescript
// Register listeners before executing workflow
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .build();

// Set up listeners BEFORE execution
workflow.on('agent.status', (event) => {
  console.log('Agent status:', event.payload);
});

// Then execute
const results = await workflow.execute('start', { metadata: {} });
```

### 3. Memory Leaks

**Symptoms:**
- Increasing memory usage over time
- Application becomes slow
- Out of memory errors

**Possible Causes:**
- Event listeners not being cleaned up
- Large objects stored in agent state
- Circular references in event payloads

**Solutions:**

#### Clean Up Event Listeners
```typescript
class MyAgent extends AgentComponent {
  private listeners: (() => void)[] = [];
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('my-agent', eventBus, telemetry);
    
    // Store cleanup functions
    this.listeners.push(
      this.eventBus.on('data.received', this.handleData.bind(this))
    );
  }
  
  cleanup() {
    // Clean up all listeners
    this.listeners.forEach(cleanup => cleanup());
    this.listeners = [];
  }
}
```

#### Manage State Size
```typescript
class StatefulAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // Avoid storing large objects in state
    const currentState = this.state.get();
    
    // Instead of storing entire objects, store references or summaries
    this.state.set({
      ...currentState,
      lastProcessedId: data.id,  // Store ID instead of full object
      processedCount: currentState.processedCount + 1
    });
    
    return { /* ... */ };
  }
}
```

### 4. LLM Provider Errors

**Symptoms:**
- ReasoningAgent failures
- API rate limit errors
- Authentication failures

**Possible Causes:**
- Invalid API keys
- Rate limiting
- Network connectivity issues
- Malformed requests

**Solutions:**

#### Robust LLM Provider Implementation
```typescript
const robustLLMProvider = async (prompt: string, context: Record<string, unknown>) => {
  const maxRetries = 3;
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30s timeout
      
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: 'gpt-4',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7
        }),
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        if (response.status === 429) {
          // Rate limited - wait and retry
          const retryAfter = parseInt(response.headers.get('retry-after') || '60');
          console.log(`Rate limited, waiting ${retryAfter}s before retry ${attempt}/${maxRetries}`);
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          continue;
        }
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data.choices[0].message.content;
      
    } catch (error) {
      lastError = error as Error;
      console.error(`LLM call attempt ${attempt} failed:`, error.message);
      
      if (attempt < maxRetries) {
        // Exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  throw new Error(`LLM provider failed after ${maxRetries} attempts: ${lastError.message}`);
};
```

### 5. Tool Execution Failures

**Symptoms:**
- ToolAgent errors
- File operation failures
- API call timeouts

**Possible Causes:**
- Invalid tool parameters
- File permission issues
- Network connectivity problems
- Tool implementation bugs

**Solutions:**

#### Robust Tool Implementation
```typescript
const robustTools = {
  'read-file': async (filePath: string) => {
    // Validate input
    if (!filePath || typeof filePath !== 'string') {
      throw new Error('Invalid file path provided');
    }
    
    try {
      // Check if file exists and is readable
      await fs.access(filePath, fs.constants.R_OK);
      
      const content = await fs.readFile(filePath, 'utf-8');
      
      return {
        success: true,
        content,
        size: content.length,
        path: filePath
      };
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error.message}`);
    }
  },
  
  'api-call': async ({ url, method = 'GET', timeout = 10000 }: { url: string; method?: string; timeout?: number }) => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    try {
      const response = await fetch(url, {
        method,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${timeout}ms`);
      }
      
      throw error;
    }
  }
};
```

## Debugging Techniques

### 1. Enable Comprehensive Logging

```typescript
// Create a debug-enabled workflow
function createDebugWorkflow() {
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('planner', llmProvider)
    .addToolAgent('executor', tools)
    .addMemoryAgent('storage')
    .route('start', 'planner')
    .route('execute', 'executor')
    .route('store', 'storage')
    .build();
  
  // Log all events
  const eventTypes = [
    'agent.status', 'agent.started', 'agent.completed', 'agent.failed',
    'step.started', 'step.completed', 'step.failed',
    'workflow.started', 'workflow.completed', 'workflow.failed'
  ];
  
  eventTypes.forEach(eventType => {
    workflow.on(eventType, (event) => {
      console.log(`[${new Date().toISOString()}] ${eventType}:`, {
        source: event.source,
        payload: event.payload,
        correlationId: event.correlationId
      });
    });
  });
  
  return workflow;
}
```

### 2. Add Performance Monitoring

```typescript
// Monitor workflow performance
workflow.on('step.completed', (event) => {
  const metrics = workflow.getMetrics();
  console.log('Performance metrics:', {
    totalExecutions: metrics.totalExecutions,
    averageDuration: metrics.averageDuration,
    successRate: metrics.successRate,
    componentStats: metrics.componentStats
  });
});
```

### 3. Implement Health Checks

```typescript
class HealthCheckAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const healthChecks = await Promise.allSettled([
      this.checkLLMProvider(),
      this.checkFileSystem(),
      this.checkNetworkConnectivity(),
      this.checkMemoryUsage()
    ]);
    
    const results = healthChecks.map((check, index) => ({
      check: ['LLM', 'FileSystem', 'Network', 'Memory'][index],
      status: check.status,
      result: check.status === 'fulfilled' ? check.value : check.reason
    }));
    
    const allHealthy = results.every(r => r.status === 'fulfilled');
    
    this.emit('health.checked', {
      overall: allHealthy ? 'healthy' : 'unhealthy',
      checks: results
    });
    
    return {
      success: allHealthy,
      data: results,
      events: [],
      nextActions: allHealthy ? ['continue'] : ['error'],
      metadata: {}
    };
  }
  
  private async checkLLMProvider() {
    // Test LLM connectivity
    return await llmProvider('test', {});
  }
  
  private async checkFileSystem() {
    // Test file system access
    const testFile = '/tmp/test.txt';
    await fs.writeFile(testFile, 'test');
    await fs.unlink(testFile);
    return 'ok';
  }
  
  private async checkNetworkConnectivity() {
    // Test network connectivity
    const response = await fetch('https://httpbin.org/status/200');
    return response.ok ? 'ok' : 'failed';
  }
  
  private async checkMemoryUsage() {
    // Check memory usage
    const usage = process.memoryUsage();
    const usedMB = usage.heapUsed / 1024 / 1024;
    return usedMB < 500 ? 'ok' : 'high';
  }
}
```

## Performance Optimization

### 1. Optimize LLM Calls

```typescript
// Implement caching for LLM responses
const cachedLLMProvider = (() => {
  const cache = new Map<string, string>();
  
  return async (prompt: string, context: Record<string, unknown>) => {
    const cacheKey = `${prompt}-${JSON.stringify(context)}`;
    
    if (cache.has(cacheKey)) {
      console.log('Cache hit for LLM call');
      return cache.get(cacheKey)!;
    }
    
    const response = await llmProvider(prompt, context);
    cache.set(cacheKey, response);
    
    // Limit cache size
    if (cache.size > 100) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    return response;
  };
})();
```

### 2. Batch Operations

```typescript
// Batch file operations
const batchFileTools = {
  'read-multiple-files': async (filePaths: string[]) => {
    const results = await Promise.allSettled(
      filePaths.map(path => fs.readFile(path, 'utf-8'))
    );
    
    return results.map((result, index) => ({
      path: filePaths[index],
      success: result.status === 'fulfilled',
      content: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason.message : null
    }));
  }
};
```

### 3. Stream Large Datasets

```typescript
// Use streaming for large datasets
import { StreamingWorkflow } from '@/pangeaflow';

async function processLargeDataset(workflow: WorkflowOrchestrator, data: any[]) {
  const streamingWorkflow = new StreamingWorkflow(workflow);
  
  // Process in batches of 10
  const results = [];
  for await (const batchResults of streamingWorkflow.processStream(
    data[Symbol.asyncIterator](),
    'start',
    10
  )) {
    results.push(...batchResults);
    console.log(`Processed batch, total results: ${results.length}`);
  }
  
  return results;
}
```

## Getting Help

### 1. Enable Debug Mode

```typescript
// Set environment variable for debug mode
process.env.PANGEAFLOW_DEBUG = 'true';

// Or enable programmatically
const workflow = WorkflowBuilder.create()
  .enableDebugMode()  // If this method exists
  .build();
```

### 2. Collect Diagnostic Information

```typescript
// Collect comprehensive diagnostic info
function collectDiagnostics(workflow: WorkflowOrchestrator) {
  return {
    metrics: workflow.getMetrics(),
    nodeVersion: process.version,
    memoryUsage: process.memoryUsage(),
    environment: {
      NODE_ENV: process.env.NODE_ENV,
      platform: process.platform,
      arch: process.arch
    },
    timestamp: new Date().toISOString()
  };
}
```

### 3. Create Minimal Reproduction

```typescript
// Create minimal example that reproduces the issue
const minimalWorkflow = WorkflowBuilder.create()
  .addReasoningAgent('test-agent', async (prompt) => 'test response')
  .route('start', 'test-agent')
  .build();

const results = await minimalWorkflow.execute('start', {
  metadata: { test: true }
});

console.log('Minimal workflow results:', results);
```

## Next Steps

- [API Reference](./api-reference.md) - Detailed API documentation
- [Examples](./examples.md) - Working code examples
- [Core Concepts](./core-concepts.md) - Understanding the architecture
