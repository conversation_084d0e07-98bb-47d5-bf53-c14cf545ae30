
import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { supabase } from '@/integrations/supabase/client';

type TrialStatus = {
  isInTrial: boolean;
  trialEndDate: string | null;
  tutorialsCreated: number;
  tutorialsLimit: number;
  canCreateTutorial: boolean;
  daysLeft: number;
};

export const useTrialStatus = () => {
  const { user } = useAuth();
  const [trialStatus, setTrialStatus] = useState<TrialStatus>({
    isInTrial: false,
    trialEndDate: null,
    tutorialsCreated: 0,
    tutorialsLimit: 5,
    canCreateTutorial: false,
    daysLeft: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      fetchTrialStatus();
    }
  }, [user]);

  const fetchTrialStatus = async () => {
    try {
      // Get user details
      const { data: userDetails, error: userError } = await supabase
        .from('user_details')
        .select('trial_end_date, tutorials_created_trial_count, trial_tutorials_limit, tier')
        .eq('id', user?.id)
        .maybeSingle();

        // When user signs up, they don't have a user_details row yet
      if(!userDetails) return;

      if (userError) throw userError;

      // Check if user can create tutorial
      const { data: canCreate, error: canCreateError } = await supabase
        .rpc('can_user_create_tutorial', { user_id: user?.id });

      if (canCreateError) throw canCreateError;

      // Check if user is in trial
      const { data: inTrial, error: trialError } = await supabase
        .rpc('is_user_in_trial', { id_user: user?.id });

      if (trialError) throw trialError;

      const trialEndDate = userDetails?.trial_end_date;
      const daysLeft = trialEndDate 
        ? Math.max(0, Math.ceil((new Date(trialEndDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
        : 0;

      setTrialStatus({
        isInTrial: inTrial,
        trialEndDate,
        tutorialsCreated: userDetails?.tutorials_created_trial_count || 0,
        tutorialsLimit: userDetails?.trial_tutorials_limit || 5,
        canCreateTutorial: canCreate,
        daysLeft
      });
    } catch (error) {
      console.error('Error fetching trial status:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshTrialStatus = () => {
    fetchTrialStatus();
  };

  return {
    trialStatus,
    loading,
    refreshTrialStatus
  };
};
