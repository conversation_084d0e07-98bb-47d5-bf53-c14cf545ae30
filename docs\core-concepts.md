# Core Concepts

Understanding PangeaFlow's architecture and design principles is essential for building effective AI workflows. This guide covers the fundamental concepts that make PangeaFlow powerful and flexible.

## Design Philosophy

PangeaFlow was designed with these core principles:

### 🔄 **Reactive by Default**
Workflows automatically respond to state changes and events, eliminating the need for manual coordination.

### 📡 **Event-Driven Architecture**
Components communicate through events, enabling loose coupling and better scalability.

### 🛡️ **Resilience First**
Built-in error handling, recovery mechanisms, and graceful degradation.

### 📊 **Observability Built-In**
Comprehensive telemetry, metrics, and monitoring without additional setup.

### 🌊 **Stream-Native**
Designed for efficient processing of large datasets and real-time data.

## Core Components

### WorkflowOrchestrator

The central coordinator that manages workflow execution:

```typescript
class WorkflowOrchestrator {
  // Manages component registration
  registerComponent(component: AgentComponent): this
  
  // Defines routing between components
  defineRoute(action: string, componentIds: ComponentId[]): this
  
  // Executes workflows
  execute(startAction: string, context: ExecutionContext): Promise<ExecutionResult[]>
  
  // Provides event handling
  on(eventType: string, handler: (event: WorkflowEvent) => void): () => void
}
```

### AgentComponent

The base class for all workflow components:

```typescript
abstract class AgentComponent {
  readonly id: ComponentId;
  protected readonly eventBus: EventBus;
  protected readonly telemetry: TelemetryCollector;
  protected readonly state: ReactiveState<Record<string, unknown>>;
  
  // Main execution method - implement this in your agents
  abstract execute(context: ExecutionContext): Promise<ExecutionResult>;
  
  // Emit events to other components
  protected emit<T>(type: string, payload: T, correlationId?: string): void;
}
```

### EventBus

Handles all inter-component communication:

```typescript
class EventBus {
  // Emit events to listeners
  emit<T>(type: string, payload: T, source: ComponentId, correlationId?: string): void;
  
  // Subscribe to events
  on(type: string, listener: (event: WorkflowEvent) => void): () => void;
}
```

### ReactiveState

Manages component state with automatic invalidation:

```typescript
class ReactiveState<T> {
  // Get current state
  get(): T;
  
  // Update state (triggers reactivity)
  set(newState: T): void;
  
  // Subscribe to state changes
  subscribe(callback: (state: T) => void): () => void;
}
```

## Workflow Execution Model

### Action-Based Routing

PangeaFlow uses action-based routing where each component returns actions that determine the next steps:

```mermaid
graph LR
    A[Component A] -->|"action: 'process'"| B[Component B]
    B -->|"action: 'validate'"| C[Component C]
    C -->|"action: 'complete'"| D[End]
    B -->|"action: 'error'"| E[Error Handler]
```

### Execution Context

Every component receives an execution context:

```typescript
interface ExecutionContext {
  metadata: Record<string, unknown>;  // Request metadata
  correlationId?: string;             // For tracing
}
```

### Execution Results

Components return structured results:

```typescript
interface ExecutionResult {
  success: boolean;                   // Execution status
  data?: unknown;                     // Result data
  error?: Error;                      // Error information
  events: WorkflowEvent[];           // Events to emit
  nextActions: string[];             // Next actions to execute
  metadata: Record<string, unknown>; // Additional metadata
}
```

## Event System Architecture

### Event Types

PangeaFlow uses a rich event system:

```typescript
// Standard workflow events
'agent.status'     // Agent status updates
'step.started'     // Step execution started
'step.completed'   // Step execution completed
'step.failed'      // Step execution failed
'workflow.started' // Workflow execution started
'workflow.completed' // Workflow execution completed
'error'            // Error occurred

// Custom events
'data.processed'   // Custom domain events
'user.action'      // User interaction events
```

### Event Structure

All events follow a consistent structure:

```typescript
interface WorkflowEvent<T = unknown> {
  id: string;                    // Unique event ID
  type: string;                  // Event type
  timestamp: number;             // When the event occurred
  source: ComponentId;           // Which component emitted it
  payload: T;                    // Event data
  correlationId?: string;        // For request tracing
}
```

## State Management

### Reactive State Updates

Components can react to state changes automatically:

```typescript
class MyAgent extends AgentComponent {
  constructor(id: string, eventBus: EventBus, telemetry: TelemetryCollector) {
    super(id, eventBus, telemetry, { status: 'idle' });
    
    // React to state changes
    this.state.subscribe((newState) => {
      if (newState.status === 'ready') {
        this.emit('agent.ready', { agentId: this.id });
      }
    });
  }
}
```

### Cross-Component State Sharing

Components can share state through events:

```typescript
// Component A updates shared state
this.emit('state.update', { 
  key: 'processedFiles', 
  value: fileList 
});

// Component B reacts to state updates
this.eventBus.on('state.update', (event) => {
  const { key, value } = event.payload;
  this.state.set({ ...this.state.get(), [key]: value });
});
```

## Error Handling Philosophy

### Hierarchical Error Boundaries

PangeaFlow implements hierarchical error handling:

1. **Component Level**: Each component handles its own errors
2. **Workflow Level**: Workflow-wide error handlers
3. **System Level**: Global error boundaries

```typescript
// Component-level error handling
async execute(context: ExecutionContext): Promise<ExecutionResult> {
  try {
    const result = await this.performWork(context);
    return { success: true, data: result, events: [], nextActions: ['continue'] };
  } catch (error) {
    // Component handles its own errors
    return { 
      success: false, 
      error, 
      events: [{ type: 'component.error', payload: error }],
      nextActions: ['retry', 'error'] 
    };
  }
}
```

### Error Recovery Strategies

Built-in recovery patterns:

```typescript
// Automatic retry with exponential backoff
.route('error', 'retry-handler')

// Fallback to alternative component
.route('error', 'fallback-component')

// Graceful degradation
.route('error', 'minimal-response-generator')
```

## Performance and Scalability

### Memory Efficiency

PangeaFlow is designed for memory efficiency:

- **Streaming Processing**: Handle large datasets without loading everything into memory
- **Lazy Evaluation**: Components only execute when needed
- **State Cleanup**: Automatic cleanup of unused state

### Telemetry Integration

Built-in performance monitoring:

```typescript
interface TelemetryData {
  componentId: ComponentId;
  operation: string;
  duration: number;
  success: boolean;
  metadata: Record<string, unknown>;
}
```

## Comparison with Traditional Approaches

### vs. Linear Workflows (like PocketFlow)

| Aspect | Traditional | PangeaFlow |
|--------|-------------|------------|
| **Coupling** | Tight coupling | Loose coupling via events |
| **State** | Manual management | Reactive state |
| **Errors** | Manual handling | Built-in recovery |
| **Monitoring** | External tools | Built-in telemetry |
| **Scalability** | Limited | Event-driven scaling |

### vs. LangGraph

| Aspect | LangGraph | PangeaFlow |
|--------|-----------|------------|
| **Focus** | Graph-based flows | Event-driven workflows |
| **State** | Graph state | Reactive state |
| **Events** | Limited | Rich event system |
| **Telemetry** | Basic | Comprehensive |
| **Recovery** | Manual | Automatic |

## Best Practices

### Component Design

1. **Single Responsibility**: Each component should have one clear purpose
2. **Stateless When Possible**: Minimize state dependencies
3. **Event-Driven Communication**: Use events instead of direct calls
4. **Error Handling**: Always handle errors gracefully

### Workflow Design

1. **Clear Action Names**: Use descriptive action names
2. **Error Routes**: Always define error handling routes
3. **Monitoring**: Set up event listeners for observability
4. **Testing**: Test individual components and full workflows

### Performance

1. **Streaming**: Use streaming for large datasets
2. **Batching**: Batch similar operations
3. **Caching**: Cache expensive operations
4. **Cleanup**: Clean up resources properly

Ready to learn about specific components? Continue with:
- [Workflow Builder](./workflow-builder.md) - Learn the builder patterns
- [Built-in Agents](./built-in-agents.md) - Explore ReasoningAgent, ToolAgent, MemoryAgent
- [Event System](./event-system.md) - Master event-driven architecture
