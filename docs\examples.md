# Examples

This section provides complete, real-world examples of PangeaFlow workflows. Each example includes full implementation code and explanations of the patterns used.

## Example 1: Code Analysis Workflow

A workflow that analyzes code repositories and generates documentation.

### Overview

This workflow demonstrates:
- Repository file fetching
- Parallel code analysis
- Content generation with LLMs
- Result aggregation and storage

### Implementation

```typescript
import { WorkflowBuilder } from '@/pangeaflow';
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';

// Custom agent for repository analysis
class RepoAnalysisAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('repo-analysis', eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { repoUrl, selectedFiles } = context.metadata;
    
    this.emit('agent.status', {
      agentName: 'RepoAnalysisAgent',
      status: 'fetching',
      message: `Fetching files from ${repoUrl}`
    });
    
    try {
      // Fetch repository files
      const files = await this.fetchRepositoryFiles(repoUrl, selectedFiles);
      
      this.emit('agent.status', {
        agentName: 'RepoAnalysisAgent',
        status: 'completed',
        message: `Fetched ${files.length} files`
      });
      
      return {
        success: true,
        data: { files },
        events: [],
        nextActions: ['analyze-code'],
        metadata: { ...context.metadata, files }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
  
  private async fetchRepositoryFiles(repoUrl: string, selectedFiles: string[]) {
    // Implementation for fetching GitHub files
    const response = await fetch(`https://api.github.com/repos/${repoUrl}/contents`);
    const contents = await response.json();
    
    return contents.filter((file: any) => 
      selectedFiles.includes(file.name) && file.type === 'file'
    );
  }
}

// LLM provider for OpenAI
const openaiProvider = async (prompt: string, context: Record<string, unknown>) => {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.7
    })
  });
  
  const data = await response.json();
  return data.choices[0].message.content;
};

// Tools for file processing
const analysisTools = {
  'extract-functions': async (code: string) => {
    // Extract function definitions from code
    const functionRegex = /function\s+(\w+)\s*\([^)]*\)\s*{/g;
    const functions = [];
    let match;
    
    while ((match = functionRegex.exec(code)) !== null) {
      functions.push(match[1]);
    }
    
    return functions;
  },
  
  'count-lines': async (code: string) => {
    return code.split('\n').length;
  },
  
  'detect-language': async (filename: string) => {
    const extension = filename.split('.').pop();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c'
    };
    
    return languageMap[extension || ''] || 'unknown';
  }
};

// Create the workflow
function createCodeAnalysisWorkflow() {
  const workflow = WorkflowBuilder.create()
    // Add custom repository analysis agent
    .addReasoningAgent('concept-extractor', openaiProvider)
    .addReasoningAgent('doc-generator', openaiProvider)
    .addToolAgent('code-analyzer', analysisTools)
    .addMemoryAgent('results-storage')
    
    // Define workflow routes
    .route('start', 'repo-analysis')
    .route('analyze-code', 'code-analyzer')
    .route('extract-concepts', 'concept-extractor')
    .route('generate-docs', 'doc-generator')
    .route('store-results', 'results-storage')
    .route('error', 'concept-extractor')  // Retry from concept extraction
    .build();
  
  // Register custom agent
  const orchestrator = workflow as any;
  const repoAgent = new RepoAnalysisAgent(
    orchestrator.eventBus,
    orchestrator.telemetry
  );
  orchestrator.registerComponent(repoAgent);
  
  return workflow;
}

// Usage
async function runCodeAnalysis() {
  const workflow = createCodeAnalysisWorkflow();
  
  // Set up event listeners
  workflow.on('agent.status', (event) => {
    console.log(`${event.payload.agentName}: ${event.payload.message}`);
  });
  
  workflow.on('workflow.completed', (event) => {
    console.log('Analysis completed:', event.payload);
  });
  
  // Execute workflow
  const results = await workflow.execute('start', {
    metadata: {
      repoUrl: 'owner/repository',
      selectedFiles: ['src/main.ts', 'src/utils.ts'],
      outputFormat: 'markdown'
    }
  });
  
  return results;
}
```

## Example 2: Data Processing Pipeline

A workflow for processing large datasets with parallel workers.

### Overview

This workflow demonstrates:
- Streaming data processing
- Parallel worker coordination
- Error handling and retries
- Result aggregation

### Implementation

```typescript
// Data validation agent
class DataValidationAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('data-validator', eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { dataset } = context.metadata;
    
    try {
      const validationResults = await this.withTelemetry('data-validation', async () => {
        return await this.validateDataset(dataset);
      });
      
      this.emit('data.validated', {
        totalRecords: dataset.length,
        validRecords: validationResults.valid.length,
        invalidRecords: validationResults.invalid.length
      });
      
      return {
        success: true,
        data: validationResults,
        events: [],
        nextActions: ['process-data'],
        metadata: { ...context.metadata, validationResults }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
  
  private async validateDataset(dataset: any[]) {
    const valid = [];
    const invalid = [];
    
    for (const record of dataset) {
      if (this.isValidRecord(record)) {
        valid.push(record);
      } else {
        invalid.push({ record, reason: 'Invalid format' });
      }
    }
    
    return { valid, invalid };
  }
  
  private isValidRecord(record: any): boolean {
    return record && typeof record === 'object' && record.id && record.data;
  }
}

// Processing tools
const processingTools = {
  'clean-data': async (records: any[]) => {
    return records.map(record => ({
      ...record,
      data: record.data.trim().toLowerCase()
    }));
  },
  
  'enrich-data': async (records: any[]) => {
    return records.map(record => ({
      ...record,
      enriched: true,
      processedAt: new Date().toISOString()
    }));
  },
  
  'aggregate-data': async (records: any[]) => {
    return {
      totalRecords: records.length,
      categories: [...new Set(records.map(r => r.category))],
      summary: records.reduce((acc, record) => {
        acc[record.category] = (acc[record.category] || 0) + 1;
        return acc;
      }, {})
    };
  }
};

// Create data processing workflow
function createDataProcessingWorkflow() {
  const workflow = WorkflowBuilder.create()
    // Data processing agents
    .addReasoningAgent('coordinator', openaiProvider)
    .addToolAgent('cleaner', { 'clean-data': processingTools['clean-data'] })
    .addToolAgent('enricher', { 'enrich-data': processingTools['enrich-data'] })
    .addToolAgent('aggregator', { 'aggregate-data': processingTools['aggregate-data'] })
    .addMemoryAgent('processed-data-store')
    
    // Define processing pipeline
    .route('start', 'data-validator')
    .route('process-data', 'coordinator')
    .route('clean', 'cleaner')
    .route('enrich', 'enricher')
    .route('aggregate', 'aggregator')
    .route('store', 'processed-data-store')
    .route('error', 'coordinator')
    .build();
  
  // Register custom validation agent
  const orchestrator = workflow as any;
  const validationAgent = new DataValidationAgent(
    orchestrator.eventBus,
    orchestrator.telemetry
  );
  orchestrator.registerComponent(validationAgent);
  
  return workflow;
}

// Usage with streaming
async function runDataProcessing() {
  const workflow = createDataProcessingWorkflow();
  
  // Sample dataset
  const dataset = [
    { id: 1, data: '  Hello World  ', category: 'greeting' },
    { id: 2, data: 'GOODBYE', category: 'farewell' },
    { id: 3, data: 'How are you?', category: 'question' },
    // ... more data
  ];
  
  // Set up monitoring
  workflow.on('data.validated', (event) => {
    console.log(`Validation: ${event.payload.validRecords}/${event.payload.totalRecords} valid`);
  });
  
  workflow.on('agent.status', (event) => {
    console.log(`${event.payload.agentName}: ${event.payload.message}`);
  });
  
  // Execute workflow
  const results = await workflow.execute('start', {
    metadata: { dataset }
  });
  
  return results;
}
```

## Example 3: Multi-Agent Coordination

A workflow demonstrating complex agent coordination and communication.

### Overview

This workflow shows:
- Agent-to-agent communication
- Conditional routing based on results
- State sharing between agents
- Dynamic workflow adaptation

### Implementation

```typescript
// Coordinator agent that manages other agents
class CoordinatorAgent extends AgentComponent {
  private taskQueue: any[] = [];
  private completedTasks: any[] = [];
  
  constructor(eventBus: any, telemetry: any) {
    super('coordinator', eventBus, telemetry, {
      activeWorkers: 0,
      totalTasks: 0,
      completedTasks: 0
    });
    
    // Listen to worker completion events
    this.eventBus.on('worker.completed', this.handleWorkerCompleted.bind(this));
    this.eventBus.on('worker.failed', this.handleWorkerFailed.bind(this));
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { tasks, maxWorkers = 3 } = context.metadata;
    
    this.taskQueue = [...tasks];
    this.state.set({
      ...this.state.get(),
      totalTasks: tasks.length
    });
    
    // Distribute initial tasks to workers
    const initialTasks = this.taskQueue.splice(0, maxWorkers);
    
    this.emit('coordinator.distributing', {
      totalTasks: tasks.length,
      initialBatch: initialTasks.length,
      remainingTasks: this.taskQueue.length
    });
    
    return {
      success: true,
      data: { distributedTasks: initialTasks },
      events: [],
      nextActions: ['process-tasks'],
      metadata: { 
        ...context.metadata, 
        distributedTasks: initialTasks,
        remainingTasks: this.taskQueue.length
      }
    };
  }
  
  private async handleWorkerCompleted(event: any) {
    const { workerId, result, task } = event.payload;
    
    this.completedTasks.push({ task, result, workerId });
    
    // Update state
    const currentState = this.state.get();
    this.state.set({
      ...currentState,
      completedTasks: this.completedTasks.length
    });
    
    // Assign next task if available
    if (this.taskQueue.length > 0) {
      const nextTask = this.taskQueue.shift();
      
      this.emit('task.assigned', {
        workerId,
        task: nextTask,
        remainingTasks: this.taskQueue.length
      });
    } else if (this.completedTasks.length === currentState.totalTasks) {
      // All tasks completed
      this.emit('coordination.completed', {
        totalCompleted: this.completedTasks.length,
        results: this.completedTasks
      });
    }
  }
  
  private async handleWorkerFailed(event: any) {
    const { workerId, task, error } = event.payload;
    
    // Re-queue failed task for retry
    this.taskQueue.push(task);
    
    this.emit('task.retried', {
      workerId,
      task,
      error,
      queueLength: this.taskQueue.length
    });
  }
}

// Worker agent that processes individual tasks
class WorkerAgent extends AgentComponent {
  constructor(id: string, eventBus: any, telemetry: any) {
    super(id, eventBus, telemetry);
    
    // Listen for task assignments
    this.eventBus.on('task.assigned', this.handleTaskAssignment.bind(this));
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { distributedTasks } = context.metadata;
    
    // Process assigned tasks
    for (const task of distributedTasks || []) {
      await this.processTask(task);
    }
    
    return {
      success: true,
      data: { processed: distributedTasks?.length || 0 },
      events: [],
      nextActions: ['wait-for-tasks'],
      metadata: context.metadata
    };
  }
  
  private async handleTaskAssignment(event: any) {
    const { workerId, task } = event.payload;
    
    if (workerId === this.id) {
      await this.processTask(task);
    }
  }
  
  private async processTask(task: any) {
    try {
      this.emit('worker.started', {
        workerId: this.id,
        task: task.id
      });
      
      // Simulate task processing
      const result = await this.withTelemetry('task-processing', async () => {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
        return { processed: true, data: task.data.toUpperCase() };
      });
      
      this.emit('worker.completed', {
        workerId: this.id,
        task,
        result
      });
      
    } catch (error) {
      this.emit('worker.failed', {
        workerId: this.id,
        task,
        error: error.message
      });
    }
  }
}

// Create coordination workflow
function createCoordinationWorkflow() {
  const workflow = WorkflowBuilder.create()
    .addMemoryAgent('task-results')
    .route('start', 'coordinator')
    .route('process-tasks', 'worker-1', 'worker-2', 'worker-3')
    .route('store-results', 'task-results')
    .build();
  
  // Register custom agents
  const orchestrator = workflow as any;
  
  const coordinator = new CoordinatorAgent(
    orchestrator.eventBus,
    orchestrator.telemetry
  );
  orchestrator.registerComponent(coordinator);
  
  // Register multiple worker agents
  for (let i = 1; i <= 3; i++) {
    const worker = new WorkerAgent(
      `worker-${i}`,
      orchestrator.eventBus,
      orchestrator.telemetry
    );
    orchestrator.registerComponent(worker);
  }
  
  return workflow;
}

// Usage
async function runCoordinationExample() {
  const workflow = createCoordinationWorkflow();
  
  // Monitor coordination events
  workflow.on('coordinator.distributing', (event) => {
    console.log('Distributing tasks:', event.payload);
  });
  
  workflow.on('worker.completed', (event) => {
    console.log(`Worker ${event.payload.workerId} completed task ${event.payload.task.id}`);
  });
  
  workflow.on('coordination.completed', (event) => {
    console.log('All tasks completed:', event.payload.totalCompleted);
  });
  
  // Sample tasks
  const tasks = Array.from({ length: 10 }, (_, i) => ({
    id: i + 1,
    data: `task-${i + 1}`,
    priority: Math.floor(Math.random() * 3) + 1
  }));
  
  const results = await workflow.execute('start', {
    metadata: { tasks, maxWorkers: 3 }
  });
  
  return results;
}
```

## Running the Examples

### Prerequisites

```bash
# Install dependencies
npm install

# Set up environment variables
export OPENAI_API_KEY="your-openai-key"
export GITHUB_TOKEN="your-github-token"
```

### Execution

```typescript
// Run individual examples
async function main() {
  console.log('Running Code Analysis Example...');
  await runCodeAnalysis();
  
  console.log('\nRunning Data Processing Example...');
  await runDataProcessing();
  
  console.log('\nRunning Coordination Example...');
  await runCoordinationExample();
}

main().catch(console.error);
```

## Key Patterns Demonstrated

1. **Custom Agent Creation**: Extending AgentComponent for specialized functionality
2. **Event-Driven Communication**: Agents communicating through events
3. **State Management**: Using reactive state for coordination
4. **Error Handling**: Comprehensive error handling and recovery
5. **Parallel Processing**: Coordinating multiple workers
6. **Telemetry Integration**: Performance monitoring throughout workflows

## Next Steps

- [Custom Agents](./custom-agents.md) - Learn to create specialized agents
- [Event System](./event-system.md) - Master event-driven patterns
- [Troubleshooting](./troubleshooting.md) - Debug common issues
