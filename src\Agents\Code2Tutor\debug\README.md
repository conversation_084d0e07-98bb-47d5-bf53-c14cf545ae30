# Code2Tutor Debugging Guide

This guide helps debug issues with the Code2Tutor workflow when tutorials are not being generated or displayed properly.

## Common Issues and Solutions

### 1. No Tutorial Output/Files Created

**Symptoms:**
- Workflow shows as completed
- No visible errors in console
- Expected tutorial files not appearing
- TutorGallery shows only demo tutorials

**Debugging Steps:**

1. **Check Browser Console Logs**
   ```javascript
   // Open browser console and look for:
   // - ConceptExtractionAgent logs
   // - Database insertion errors
   // - LLM response parsing errors
   ```

2. **Test Individual Components**
   ```javascript
   // In browser console:
   import('./src/Agents/Code2Tutor/debug/testWorkflow.ts').then(module => {
     module.testCode2TutorWorkflow();
   });
   ```

3. **Check Database Schema Compatibility**
   - The TutorialAssemblyAgent was updated to map Code2Tutor fields to the existing tutorial_metadata schema
   - Missing fields: `tutorial_type`, `estimated_time`, `learning_objectives`, `prerequisites`
   - These fields need to be added to the database schema for full functionality

### 2. LLM Response Parsing Errors

**Symptoms:**
- ConceptExtractionAgent fails silently
- "Failed to parse concepts" errors in console

**Debugging Steps:**

1. **Check LLM Response Format**
   - Look for "ConceptExtractionAgent - Raw LLM response:" in console
   - Verify YAML format is correct
   - Check if response contains ```yaml blocks

2. **Validate YAML Structure**
   ```yaml
   concepts:
     - name: "Concept Name"
       description: "Description"
       difficulty: "beginner"
       prerequisites: []
       file_indices: [0, 1]
       examples: ["Example 1"]
   ```

### 3. Database Connection Issues

**Symptoms:**
- "Database error" messages in console
- Tutorials not saving to Supabase

**Debugging Steps:**

1. **Check Supabase Configuration**
   ```typescript
   // Verify in src/integrations/supabase/client.ts
   const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";
   const SUPABASE_ANON_KEY = "...";
   ```

2. **Verify Database Schema**
   - Check if `tutorial_metadata` table exists
   - Verify required fields are present
   - Check user permissions

3. **Test Database Connection**
   ```javascript
   // In browser console:
   import { supabase } from '@/integrations/supabase/client';
   supabase.from('tutorial_metadata').select('count').then(console.log);
   ```

### 4. Event Handling Issues

**Symptoms:**
- UI not updating during workflow
- Progress indicators not working
- No status messages

**Debugging Steps:**

1. **Check Event Listeners**
   ```javascript
   // In TutorCreationStatus component
   // Verify event listeners are properly set up
   tutorEvents.on(TutorEventType.PROGRESS, handleProgress);
   ```

2. **Monitor Event Emissions**
   ```javascript
   // Add to browser console:
   import { tutorEvents, TutorEventType } from '@/Agents/Code2Tutor/utils/events';
   Object.values(TutorEventType).forEach(eventType => {
     tutorEvents.on(eventType, (data) => console.log(`Event: ${eventType}`, data));
   });
   ```

## Fixed Issues

### 1. Database Schema Mismatch ✅
- **Problem**: TutorialAssemblyAgent was trying to insert fields that don't exist in tutorial_metadata table
- **Solution**: Updated field mapping to use existing schema fields
- **Changes**: 
  - `target_audience` → `difficulty`
  - `learning_objectives` → `tags`
  - Removed non-existent fields

### 2. Missing TypeScript Types ✅
- **Problem**: js-yaml module had no type definitions
- **Solution**: Installed `@types/js-yaml`
- **Command**: `npm install --save-dev @types/js-yaml`

### 3. Silent Error Handling ✅
- **Problem**: Errors were not being logged properly
- **Solution**: Added comprehensive logging and error handling
- **Changes**:
  - Enhanced ConceptExtractionAgent logging
  - Improved TutorialAssemblyAgent error reporting
  - Better workflow execution monitoring

### 4. TutorGallery Not Showing Real Tutorials ✅
- **Problem**: Gallery was only showing demo tutorials
- **Solution**: Updated to fetch real tutorials from Supabase
- **Changes**: Added proper Supabase integration with fallback to demo data

## Testing Tools

### 1. Workflow Test Script
```typescript
// Use the test script to debug workflow execution
import { testCode2TutorWorkflow } from './testWorkflow';
testCode2TutorWorkflow();
```

### 2. Browser Console Commands
```javascript
// Test event system
window.testCode2TutorWorkflow();

// Check Supabase connection
import('@/integrations/supabase/client').then(({supabase}) => {
  supabase.from('tutorial_metadata').select('*').limit(5).then(console.log);
});

// Monitor all events
import('@/Agents/Code2Tutor/utils/events').then(({tutorEvents, TutorEventType}) => {
  Object.values(TutorEventType).forEach(type => {
    tutorEvents.on(type, data => console.log(`[${type}]`, data));
  });
});
```

## Next Steps for Full Resolution

1. **Database Schema Updates** (Recommended)
   ```sql
   ALTER TABLE tutorial_metadata 
   ADD COLUMN tutorial_type VARCHAR(50),
   ADD COLUMN estimated_time INTEGER,
   ADD COLUMN learning_objectives JSONB,
   ADD COLUMN prerequisites JSONB;
   ```

2. **Enhanced Error Reporting**
   - Add toast notifications for user feedback
   - Implement retry mechanisms for failed operations
   - Add progress persistence across page refreshes

3. **Performance Optimization**
   - Implement proper caching for LLM responses
   - Add background job processing for long-running workflows
   - Optimize file processing for large repositories

## Monitoring Workflow Execution

The workflow now includes comprehensive logging at each stage:

1. **Repository Fetching**: File selection and processing
2. **Concept Extraction**: LLM analysis and YAML parsing
3. **Tutorial Planning**: Structure generation
4. **Content Generation**: Section and exercise creation
5. **Tutorial Assembly**: Final compilation and storage

Each stage emits detailed progress events and error information for debugging.
