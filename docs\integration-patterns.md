# Integration Patterns

This guide covers how to integrate PangeaFlow with external systems, including LangGraph, databases, APIs, and other workflow engines. Learn patterns for building robust, scalable integrations.

## LangGraph Integration

PangeaFlow can work alongside LangGraph or serve as a replacement with enhanced capabilities.

### Running LangGraph Workflows in PangeaFlow

```typescript
import { StateGraph } from '@langchain/langgraph';

class LangGraphIntegrationAgent extends AgentComponent {
  private langGraphWorkflow: StateGraph;
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, langGraphWorkflow: StateGraph) {
    super('langgraph-integration', eventBus, telemetry);
    this.langGraphWorkflow = langGraphWorkflow;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      this.emit('langgraph.started', {
        workflowId: this.langGraphWorkflow.id,
        input: context.metadata
      });
      
      // Execute LangGraph workflow
      const result = await this.withTelemetry('langgraph-execution', async () => {
        return await this.langGraphWorkflow.invoke(context.metadata);
      });
      
      this.emit('langgraph.completed', {
        workflowId: this.langGraphWorkflow.id,
        result
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, langGraphResult: result }
      };
      
    } catch (error) {
      this.emit('langgraph.failed', {
        workflowId: this.langGraphWorkflow.id,
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}

// Usage
function createHybridWorkflow() {
  // Create LangGraph workflow
  const langGraphWorkflow = new StateGraph({
    // LangGraph configuration
  });
  
  // Integrate with PangeaFlow
  const workflow = WorkflowBuilder.create()
    .addReasoningAgent('preprocessor', llmProvider)
    .addMemoryAgent('storage')
    .route('start', 'preprocessor')
    .route('run-langgraph', 'langgraph-integration')
    .route('store-results', 'storage')
    .build();
  
  // Register LangGraph integration agent
  const orchestrator = workflow as any;
  const langGraphAgent = new LangGraphIntegrationAgent(
    orchestrator.eventBus,
    orchestrator.telemetry,
    langGraphWorkflow
  );
  orchestrator.registerComponent(langGraphAgent);
  
  return workflow;
}
```

### Migrating from LangGraph to PangeaFlow

```typescript
// LangGraph workflow
const langGraphWorkflow = new StateGraph({
  channels: {
    input: { value: null },
    output: { value: null }
  }
})
.addNode('process', processNode)
.addNode('validate', validateNode)
.addEdge('process', 'validate')
.setEntryPoint('process');

// Equivalent PangeaFlow workflow
const pangeaFlowWorkflow = WorkflowBuilder.create()
  .addToolAgent('processor', { 'process': processFunction })
  .addToolAgent('validator', { 'validate': validateFunction })
  .addMemoryAgent('state-manager')
  .route('start', 'processor')
  .route('validate', 'validator')
  .route('complete', 'state-manager')
  .build();
```

## Database Integration

### SQL Database Integration

```typescript
class DatabaseAgent extends AgentComponent {
  private db: any; // Your database connection
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, dbConnection: any) {
    super('database-agent', eventBus, telemetry);
    this.db = dbConnection;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { operation, query, params } = context.metadata;
    
    try {
      const result = await this.withTelemetry(`db-${operation}`, async () => {
        switch (operation) {
          case 'select':
            return await this.db.query(query, params);
          case 'insert':
            return await this.db.insert(query, params);
          case 'update':
            return await this.db.update(query, params);
          case 'delete':
            return await this.db.delete(query, params);
          default:
            throw new Error(`Unsupported operation: ${operation}`);
        }
      });
      
      this.emit('database.operation.completed', {
        operation,
        rowsAffected: result.rowsAffected || result.length,
        executionTime: Date.now()
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, dbResult: result }
      };
      
    } catch (error) {
      this.emit('database.operation.failed', {
        operation,
        error: error.message,
        query
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}

// Database tools for ToolAgent
const databaseTools = {
  'query-users': async (params: { limit?: number; offset?: number }) => {
    const { limit = 10, offset = 0 } = params;
    return await db.query('SELECT * FROM users LIMIT ? OFFSET ?', [limit, offset]);
  },
  
  'create-user': async (userData: { name: string; email: string }) => {
    return await db.insert('INSERT INTO users (name, email) VALUES (?, ?)', [userData.name, userData.email]);
  },
  
  'update-user': async (params: { id: number; updates: Record<string, any> }) => {
    const { id, updates } = params;
    const fields = Object.keys(updates).map(key => `${key} = ?`).join(', ');
    const values = [...Object.values(updates), id];
    return await db.update(`UPDATE users SET ${fields} WHERE id = ?`, values);
  }
};
```

### NoSQL Database Integration

```typescript
class MongoDBAgent extends AgentComponent {
  private collection: any; // MongoDB collection
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, collection: any) {
    super('mongodb-agent', eventBus, telemetry);
    this.collection = collection;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { operation, filter, document, options } = context.metadata;
    
    try {
      const result = await this.withTelemetry(`mongodb-${operation}`, async () => {
        switch (operation) {
          case 'find':
            return await this.collection.find(filter, options).toArray();
          case 'findOne':
            return await this.collection.findOne(filter, options);
          case 'insertOne':
            return await this.collection.insertOne(document);
          case 'insertMany':
            return await this.collection.insertMany(document);
          case 'updateOne':
            return await this.collection.updateOne(filter, document, options);
          case 'updateMany':
            return await this.collection.updateMany(filter, document, options);
          case 'deleteOne':
            return await this.collection.deleteOne(filter);
          case 'deleteMany':
            return await this.collection.deleteMany(filter);
          case 'aggregate':
            return await this.collection.aggregate(document).toArray();
          default:
            throw new Error(`Unsupported MongoDB operation: ${operation}`);
        }
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, mongoResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## API Integration

### REST API Integration

```typescript
class RestApiAgent extends AgentComponent {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  
  constructor(
    eventBus: EventBus, 
    telemetry: TelemetryCollector, 
    baseUrl: string,
    defaultHeaders: Record<string, string> = {}
  ) {
    super('rest-api-agent', eventBus, telemetry);
    this.baseUrl = baseUrl;
    this.defaultHeaders = defaultHeaders;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { method, endpoint, data, headers = {} } = context.metadata;
    
    try {
      const result = await this.withTelemetry(`api-${method.toLowerCase()}`, async () => {
        const response = await fetch(`${this.baseUrl}${endpoint}`, {
          method: method.toUpperCase(),
          headers: { ...this.defaultHeaders, ...headers },
          body: data ? JSON.stringify(data) : undefined
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.json();
      });
      
      this.emit('api.request.completed', {
        method,
        endpoint,
        status: 'success',
        responseSize: JSON.stringify(result).length
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, apiResult: result }
      };
      
    } catch (error) {
      this.emit('api.request.failed', {
        method,
        endpoint,
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['retry', 'error'],
        metadata: context.metadata
      };
    }
  }
}

// REST API tools
const apiTools = {
  'get-user': async (userId: string) => {
    const response = await fetch(`/api/users/${userId}`);
    return await response.json();
  },
  
  'create-post': async (postData: { title: string; content: string; userId: string }) => {
    const response = await fetch('/api/posts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(postData)
    });
    return await response.json();
  },
  
  'upload-file': async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
    return await response.json();
  }
};
```

### GraphQL Integration

```typescript
class GraphQLAgent extends AgentComponent {
  private endpoint: string;
  private headers: Record<string, string>;
  
  constructor(
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    endpoint: string,
    headers: Record<string, string> = {}
  ) {
    super('graphql-agent', eventBus, telemetry);
    this.endpoint = endpoint;
    this.headers = headers;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { query, variables = {}, operationName } = context.metadata;
    
    try {
      const result = await this.withTelemetry('graphql-query', async () => {
        const response = await fetch(this.endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.headers
          },
          body: JSON.stringify({
            query,
            variables,
            operationName
          })
        });
        
        const data = await response.json();
        
        if (data.errors) {
          throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
        }
        
        return data.data;
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, graphqlResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## Message Queue Integration

### Redis/Bull Queue Integration

```typescript
class QueueAgent extends AgentComponent {
  private queue: any; // Bull queue instance
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, queue: any) {
    super('queue-agent', eventBus, telemetry);
    this.queue = queue;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { operation, jobData, jobOptions = {} } = context.metadata;
    
    try {
      let result;
      
      switch (operation) {
        case 'add':
          result = await this.withTelemetry('queue-add', async () => {
            return await this.queue.add(jobData, jobOptions);
          });
          break;
          
        case 'process':
          result = await this.withTelemetry('queue-process', async () => {
            return await this.processQueueJob(jobData);
          });
          break;
          
        case 'getWaiting':
          result = await this.queue.getWaiting();
          break;
          
        case 'getActive':
          result = await this.queue.getActive();
          break;
          
        case 'getCompleted':
          result = await this.queue.getCompleted();
          break;
          
        default:
          throw new Error(`Unsupported queue operation: ${operation}`);
      }
      
      this.emit('queue.operation.completed', {
        operation,
        jobCount: Array.isArray(result) ? result.length : 1
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, queueResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
  
  private async processQueueJob(jobData: any): Promise<any> {
    // Process the job data
    return {
      processed: true,
      result: jobData,
      processedAt: Date.now()
    };
  }
}
```

### Apache Kafka Integration

```typescript
class KafkaAgent extends AgentComponent {
  private producer: any;
  private consumer: any;
  
  constructor(
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    producer: any,
    consumer: any
  ) {
    super('kafka-agent', eventBus, telemetry);
    this.producer = producer;
    this.consumer = consumer;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { operation, topic, message, consumerConfig } = context.metadata;
    
    try {
      let result;
      
      switch (operation) {
        case 'produce':
          result = await this.withTelemetry('kafka-produce', async () => {
            return await this.producer.send({
              topic,
              messages: Array.isArray(message) ? message : [message]
            });
          });
          break;
          
        case 'consume':
          result = await this.withTelemetry('kafka-consume', async () => {
            return await this.consumeMessages(topic, consumerConfig);
          });
          break;
          
        default:
          throw new Error(`Unsupported Kafka operation: ${operation}`);
      }
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, kafkaResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
  
  private async consumeMessages(topic: string, config: any): Promise<any[]> {
    const messages: any[] = [];
    
    await this.consumer.subscribe({ topic });
    
    await this.consumer.run({
      eachMessage: async ({ message }: any) => {
        messages.push({
          key: message.key?.toString(),
          value: message.value?.toString(),
          timestamp: message.timestamp,
          offset: message.offset
        });
      }
    });
    
    return messages;
  }
}
```

## External Service Integration

### Email Service Integration

```typescript
class EmailAgent extends AgentComponent {
  private emailService: any; // Nodemailer or similar
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, emailService: any) {
    super('email-agent', eventBus, telemetry);
    this.emailService = emailService;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { to, subject, text, html, attachments } = context.metadata;
    
    try {
      const result = await this.withTelemetry('email-send', async () => {
        return await this.emailService.sendMail({
          to,
          subject,
          text,
          html,
          attachments
        });
      });
      
      this.emit('email.sent', {
        to,
        subject,
        messageId: result.messageId
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, emailResult: result }
      };
      
    } catch (error) {
      this.emit('email.failed', {
        to,
        subject,
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['retry', 'error'],
        metadata: context.metadata
      };
    }
  }
}
```

### File Storage Integration

```typescript
class FileStorageAgent extends AgentComponent {
  private storageClient: any; // AWS S3, Google Cloud Storage, etc.
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, storageClient: any) {
    super('file-storage-agent', eventBus, telemetry);
    this.storageClient = storageClient;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { operation, bucket, key, data, options = {} } = context.metadata;
    
    try {
      let result;
      
      switch (operation) {
        case 'upload':
          result = await this.withTelemetry('storage-upload', async () => {
            return await this.storageClient.upload({
              Bucket: bucket,
              Key: key,
              Body: data,
              ...options
            }).promise();
          });
          break;
          
        case 'download':
          result = await this.withTelemetry('storage-download', async () => {
            return await this.storageClient.getObject({
              Bucket: bucket,
              Key: key
            }).promise();
          });
          break;
          
        case 'delete':
          result = await this.withTelemetry('storage-delete', async () => {
            return await this.storageClient.deleteObject({
              Bucket: bucket,
              Key: key
            }).promise();
          });
          break;
          
        case 'list':
          result = await this.withTelemetry('storage-list', async () => {
            return await this.storageClient.listObjectsV2({
              Bucket: bucket,
              Prefix: key
            }).promise();
          });
          break;
          
        default:
          throw new Error(`Unsupported storage operation: ${operation}`);
      }
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, storageResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['retry', 'error'],
        metadata: context.metadata
      };
    }
  }
}
```

## Integration Best Practices

### 1. Connection Pooling

```typescript
class ConnectionPoolManager {
  private pools = new Map<string, any>();
  
  getConnection(type: string, config: any): any {
    const poolKey = `${type}-${JSON.stringify(config)}`;
    
    if (!this.pools.has(poolKey)) {
      const pool = this.createPool(type, config);
      this.pools.set(poolKey, pool);
    }
    
    return this.pools.get(poolKey);
  }
  
  private createPool(type: string, config: any): any {
    switch (type) {
      case 'mysql':
        return mysql.createPool(config);
      case 'postgres':
        return new Pool(config);
      case 'redis':
        return new Redis(config);
      default:
        throw new Error(`Unsupported connection type: ${type}`);
    }
  }
  
  async closeAll(): Promise<void> {
    for (const [key, pool] of this.pools) {
      try {
        await pool.end();
      } catch (error) {
        console.error(`Failed to close pool ${key}:`, error);
      }
    }
    this.pools.clear();
  }
}
```

### 2. Circuit Breaker for External Services

```typescript
class ExternalServiceAgent extends AgentComponent {
  private circuitBreaker: CircuitBreaker;
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, serviceUrl: string) {
    super('external-service-agent', eventBus, telemetry);
    
    this.circuitBreaker = new CircuitBreaker(
      this.callExternalService.bind(this),
      {
        timeout: 5000,
        errorThresholdPercentage: 50,
        resetTimeout: 30000
      }
    );
    
    this.circuitBreaker.on('open', () => {
      this.emit('circuit.opened', { service: serviceUrl });
    });
    
    this.circuitBreaker.on('halfOpen', () => {
      this.emit('circuit.halfOpen', { service: serviceUrl });
    });
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.circuitBreaker.fire(context.metadata);
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, serviceResult: result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['fallback'],
        metadata: context.metadata
      };
    }
  }
  
  private async callExternalService(params: any): Promise<any> {
    // External service call implementation
    const response = await fetch('/external-api', {
      method: 'POST',
      body: JSON.stringify(params)
    });
    
    if (!response.ok) {
      throw new Error(`Service error: ${response.statusText}`);
    }
    
    return await response.json();
  }
}
```

### 3. Rate Limiting

```typescript
class RateLimitedAgent extends AgentComponent {
  private rateLimiter: any; // Rate limiter instance
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector, rateLimiter: any) {
    super('rate-limited-agent', eventBus, telemetry);
    this.rateLimiter = rateLimiter;
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      // Check rate limit
      const allowed = await this.rateLimiter.consume(context.metadata.userId || 'anonymous');
      
      if (!allowed) {
        this.emit('rate.limit.exceeded', {
          userId: context.metadata.userId,
          limit: this.rateLimiter.points,
          windowMs: this.rateLimiter.duration
        });
        
        return {
          success: false,
          error: new Error('Rate limit exceeded'),
          events: [],
          nextActions: ['wait', 'retry'],
          metadata: context.metadata
        };
      }
      
      // Proceed with operation
      const result = await this.performOperation(context.metadata);
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, result }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

## Next Steps

- [Examples](./examples.md) - See integration patterns in action
- [Error Handling](./error-handling.md) - Handle integration failures
- [Telemetry & Monitoring](./telemetry-monitoring.md) - Monitor external integrations
