# Streaming & Batch Processing

PangeaFlow provides powerful capabilities for processing large datasets efficiently through streaming and batch processing. This guide covers how to handle memory-intensive operations and high-throughput scenarios.

## Overview

PangeaFlow's streaming and batch processing features include:
- **Memory-efficient streaming** for large datasets
- **Configurable batch sizes** for optimal performance
- **Parallel batch processing** for improved throughput
- **Backpressure handling** to prevent memory overflow
- **Progress tracking** for long-running operations

## StreamingWorkflow

The StreamingWorkflow class enables processing of large datasets without loading everything into memory.

### Basic Usage

```typescript
import { StreamingWorkflow } from '@/pangeaflow';

async function processLargeDataset() {
  const workflow = WorkflowBuilder.create()
    .addToolAgent('processor', processingTools)
    .addMemoryAgent('results')
    .route('start', 'processor')
    .route('store', 'results')
    .build();
  
  const streamingWorkflow = new StreamingWorkflow(workflow);
  
  // Process data in batches of 50
  const results = [];
  for await (const batchResults of streamingWorkflow.processStream(
    dataGenerator(), // AsyncIterable data source
    'start',         // Starting action
    50              // Batch size
  )) {
    results.push(...batchResults);
    console.log(`Processed batch, total results: ${results.length}`);
  }
  
  return results;
}

// Data generator function
async function* dataGenerator() {
  for (let i = 0; i < 10000; i++) {
    yield { id: i, data: `item-${i}` };
  }
}
```

### Advanced Streaming Configuration

```typescript
class AdvancedStreamingProcessor {
  private streamingWorkflow: StreamingWorkflow;
  private batchSize: number;
  private maxConcurrentBatches: number;
  
  constructor(
    workflow: WorkflowOrchestrator,
    batchSize = 100,
    maxConcurrentBatches = 3
  ) {
    this.streamingWorkflow = new StreamingWorkflow(workflow);
    this.batchSize = batchSize;
    this.maxConcurrentBatches = maxConcurrentBatches;
  }
  
  async processWithBackpressure<T>(
    dataStream: AsyncIterable<T>,
    startAction: string
  ): Promise<any[]> {
    const results: any[] = [];
    const activeBatches = new Set<Promise<any>>();
    
    for await (const batchResults of this.streamingWorkflow.processStream(
      dataStream,
      startAction,
      this.batchSize
    )) {
      // Process batch asynchronously
      const batchPromise = this.processBatch(batchResults);
      activeBatches.add(batchPromise);
      
      // Handle backpressure - wait if too many concurrent batches
      if (activeBatches.size >= this.maxConcurrentBatches) {
        const completedBatch = await Promise.race(activeBatches);
        activeBatches.delete(completedBatch);
        results.push(...completedBatch);
      }
    }
    
    // Wait for remaining batches
    const remainingResults = await Promise.all(activeBatches);
    remainingResults.forEach(batchResult => results.push(...batchResult));
    
    return results;
  }
  
  private async processBatch(batchResults: any[]): Promise<any[]> {
    // Process individual batch
    return batchResults.map(result => ({
      ...result,
      processedAt: Date.now()
    }));
  }
}
```

## Batch Processing Patterns

### Fixed-Size Batching

```typescript
class BatchProcessor {
  private batchSize: number;
  private currentBatch: any[] = [];
  private workflow: WorkflowOrchestrator;
  
  constructor(workflow: WorkflowOrchestrator, batchSize = 50) {
    this.workflow = workflow;
    this.batchSize = batchSize;
  }
  
  async addItem(item: any): Promise<any[]> {
    this.currentBatch.push(item);
    
    if (this.currentBatch.length >= this.batchSize) {
      return await this.processBatch();
    }
    
    return [];
  }
  
  async flush(): Promise<any[]> {
    if (this.currentBatch.length > 0) {
      return await this.processBatch();
    }
    return [];
  }
  
  private async processBatch(): Promise<any[]> {
    const batch = [...this.currentBatch];
    this.currentBatch = [];
    
    const results = await this.workflow.execute('process-batch', {
      metadata: { batch }
    });
    
    return results.flatMap(result => result.data || []);
  }
}

// Usage
const processor = new BatchProcessor(workflow, 25);

// Add items one by one
for (const item of largeDataset) {
  const results = await processor.addItem(item);
  if (results.length > 0) {
    console.log(`Batch processed: ${results.length} items`);
  }
}

// Process remaining items
const finalResults = await processor.flush();
```

### Time-Based Batching

```typescript
class TimeBatchProcessor {
  private currentBatch: any[] = [];
  private batchTimeout: NodeJS.Timeout | null = null;
  private maxBatchSize: number;
  private maxWaitTime: number;
  private workflow: WorkflowOrchestrator;
  
  constructor(
    workflow: WorkflowOrchestrator,
    maxBatchSize = 100,
    maxWaitTime = 5000 // 5 seconds
  ) {
    this.workflow = workflow;
    this.maxBatchSize = maxBatchSize;
    this.maxWaitTime = maxWaitTime;
  }
  
  async addItem(item: any): Promise<void> {
    this.currentBatch.push(item);
    
    // Start timer if this is the first item in batch
    if (this.currentBatch.length === 1) {
      this.startBatchTimer();
    }
    
    // Process immediately if batch is full
    if (this.currentBatch.length >= this.maxBatchSize) {
      await this.processBatch();
    }
  }
  
  private startBatchTimer(): void {
    this.batchTimeout = setTimeout(async () => {
      if (this.currentBatch.length > 0) {
        await this.processBatch();
      }
    }, this.maxWaitTime);
  }
  
  private async processBatch(): Promise<void> {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }
    
    if (this.currentBatch.length === 0) return;
    
    const batch = [...this.currentBatch];
    this.currentBatch = [];
    
    console.log(`Processing time-based batch: ${batch.length} items`);
    
    try {
      await this.workflow.execute('process-time-batch', {
        metadata: { 
          batch,
          batchType: 'time-based',
          timestamp: Date.now()
        }
      });
    } catch (error) {
      console.error('Batch processing failed:', error);
      // Could implement retry logic here
    }
  }
  
  async shutdown(): Promise<void> {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }
    
    // Process any remaining items
    if (this.currentBatch.length > 0) {
      await this.processBatch();
    }
  }
}
```

### Parallel Batch Processing

```typescript
class ParallelBatchProcessor {
  private workflow: WorkflowOrchestrator;
  private maxConcurrency: number;
  private activeBatches = new Set<Promise<any>>();
  
  constructor(workflow: WorkflowOrchestrator, maxConcurrency = 5) {
    this.workflow = workflow;
    this.maxConcurrency = maxConcurrency;
  }
  
  async processDataset<T>(
    dataset: T[],
    batchSize: number
  ): Promise<any[]> {
    const batches = this.createBatches(dataset, batchSize);
    const results: any[] = [];
    
    for (const batch of batches) {
      // Wait if we've reached max concurrency
      if (this.activeBatches.size >= this.maxConcurrency) {
        const completedBatch = await Promise.race(this.activeBatches);
        this.activeBatches.delete(completedBatch);
        results.push(...completedBatch);
      }
      
      // Start processing new batch
      const batchPromise = this.processSingleBatch(batch);
      this.activeBatches.add(batchPromise);
    }
    
    // Wait for all remaining batches
    const remainingResults = await Promise.all(this.activeBatches);
    remainingResults.forEach(batchResult => results.push(...batchResult));
    
    return results;
  }
  
  private createBatches<T>(dataset: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < dataset.length; i += batchSize) {
      batches.push(dataset.slice(i, i + batchSize));
    }
    return batches;
  }
  
  private async processSingleBatch(batch: any[]): Promise<any[]> {
    try {
      const results = await this.workflow.execute('process-parallel-batch', {
        metadata: { 
          batch,
          batchId: crypto.randomUUID(),
          timestamp: Date.now()
        }
      });
      
      return results.flatMap(result => result.data || []);
    } catch (error) {
      console.error('Parallel batch processing failed:', error);
      throw error;
    }
  }
}

// Usage
const parallelProcessor = new ParallelBatchProcessor(workflow, 3);
const results = await parallelProcessor.processDataset(largeDataset, 50);
```

## Memory Management

### Memory-Efficient Data Processing

```typescript
class MemoryEfficientProcessor extends AgentComponent {
  private memoryThreshold = 500 * 1024 * 1024; // 500MB
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { dataStream } = context.metadata;
    
    try {
      const results = await this.processStreamWithMemoryMonitoring(dataStream);
      
      return {
        success: true,
        data: { processedCount: results.length },
        events: [],
        nextActions: ['continue'],
        metadata: { ...context.metadata, results }
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
  
  private async processStreamWithMemoryMonitoring(dataStream: AsyncIterable<any>) {
    const results = [];
    let processedCount = 0;
    
    for await (const item of dataStream) {
      // Check memory usage before processing
      const memoryUsage = process.memoryUsage().heapUsed;
      
      if (memoryUsage > this.memoryThreshold) {
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
        
        // Emit memory warning
        this.emit('memory.warning', {
          heapUsed: memoryUsage,
          threshold: this.memoryThreshold,
          processedCount
        });
        
        // Optionally pause processing
        await this.pauseForMemoryRecovery();
      }
      
      // Process item
      const processedItem = await this.processItem(item);
      results.push(processedItem);
      processedCount++;
      
      // Emit progress every 1000 items
      if (processedCount % 1000 === 0) {
        this.emit('processing.progress', {
          processedCount,
          memoryUsage: process.memoryUsage().heapUsed
        });
      }
    }
    
    return results;
  }
  
  private async pauseForMemoryRecovery(): Promise<void> {
    // Wait for memory to be freed
    return new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  private async processItem(item: any): Promise<any> {
    // Process individual item
    return {
      ...item,
      processed: true,
      timestamp: Date.now()
    };
  }
}
```

### Streaming with Backpressure

```typescript
class BackpressureStreamProcessor {
  private maxBufferSize: number;
  private currentBufferSize = 0;
  private processingQueue: any[] = [];
  private isProcessing = false;
  
  constructor(maxBufferSize = 1000) {
    this.maxBufferSize = maxBufferSize;
  }
  
  async processStream(
    dataStream: AsyncIterable<any>,
    processor: (item: any) => Promise<any>
  ): Promise<any[]> {
    const results: any[] = [];
    
    for await (const item of dataStream) {
      // Apply backpressure if buffer is full
      while (this.currentBufferSize >= this.maxBufferSize) {
        await this.waitForBufferSpace();
      }
      
      // Add item to processing queue
      this.processingQueue.push(item);
      this.currentBufferSize++;
      
      // Start processing if not already running
      if (!this.isProcessing) {
        this.startProcessing(processor, results);
      }
    }
    
    // Wait for all items to be processed
    while (this.processingQueue.length > 0 || this.isProcessing) {
      await new Promise(resolve => setTimeout(resolve, 10));
    }
    
    return results;
  }
  
  private async startProcessing(
    processor: (item: any) => Promise<any>,
    results: any[]
  ): Promise<void> {
    this.isProcessing = true;
    
    while (this.processingQueue.length > 0) {
      const item = this.processingQueue.shift();
      this.currentBufferSize--;
      
      try {
        const result = await processor(item);
        results.push(result);
      } catch (error) {
        console.error('Item processing failed:', error);
        // Could implement retry logic here
      }
    }
    
    this.isProcessing = false;
  }
  
  private async waitForBufferSpace(): Promise<void> {
    return new Promise(resolve => {
      const checkBuffer = () => {
        if (this.currentBufferSize < this.maxBufferSize) {
          resolve();
        } else {
          setTimeout(checkBuffer, 10);
        }
      };
      checkBuffer();
    });
  }
}
```

## Performance Optimization

### Adaptive Batch Sizing

```typescript
class AdaptiveBatchProcessor {
  private minBatchSize = 10;
  private maxBatchSize = 1000;
  private currentBatchSize = 50;
  private targetProcessingTime = 2000; // 2 seconds
  private performanceHistory: number[] = [];
  
  async processBatch(items: any[]): Promise<any[]> {
    const startTime = Date.now();
    
    // Process the batch
    const results = await this.performBatchProcessing(items);
    
    const processingTime = Date.now() - startTime;
    this.updateBatchSize(processingTime);
    
    return results;
  }
  
  private updateBatchSize(processingTime: number): void {
    this.performanceHistory.push(processingTime);
    
    // Keep only last 10 measurements
    if (this.performanceHistory.length > 10) {
      this.performanceHistory.shift();
    }
    
    const averageTime = this.performanceHistory.reduce((a, b) => a + b, 0) / this.performanceHistory.length;
    
    if (averageTime < this.targetProcessingTime * 0.8) {
      // Processing is fast, increase batch size
      this.currentBatchSize = Math.min(
        this.maxBatchSize,
        Math.floor(this.currentBatchSize * 1.2)
      );
    } else if (averageTime > this.targetProcessingTime * 1.2) {
      // Processing is slow, decrease batch size
      this.currentBatchSize = Math.max(
        this.minBatchSize,
        Math.floor(this.currentBatchSize * 0.8)
      );
    }
    
    console.log(`Adaptive batch size: ${this.currentBatchSize} (avg time: ${averageTime}ms)`);
  }
  
  getCurrentBatchSize(): number {
    return this.currentBatchSize;
  }
}
```

### Progress Tracking

```typescript
class ProgressTracker {
  private totalItems: number;
  private processedItems = 0;
  private startTime = Date.now();
  private lastReportTime = Date.now();
  private reportInterval = 5000; // 5 seconds
  
  constructor(totalItems: number) {
    this.totalItems = totalItems;
  }
  
  updateProgress(itemsProcessed: number): void {
    this.processedItems += itemsProcessed;
    
    const now = Date.now();
    if (now - this.lastReportTime >= this.reportInterval) {
      this.reportProgress();
      this.lastReportTime = now;
    }
  }
  
  private reportProgress(): void {
    const elapsed = Date.now() - this.startTime;
    const progress = (this.processedItems / this.totalItems) * 100;
    const rate = this.processedItems / (elapsed / 1000); // items per second
    const eta = (this.totalItems - this.processedItems) / rate; // seconds
    
    console.log(`Progress: ${progress.toFixed(1)}% (${this.processedItems}/${this.totalItems})`);
    console.log(`Rate: ${rate.toFixed(1)} items/sec, ETA: ${this.formatTime(eta)}`);
  }
  
  private formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  }
  
  complete(): void {
    const totalTime = Date.now() - this.startTime;
    const averageRate = this.processedItems / (totalTime / 1000);
    
    console.log(`Processing complete!`);
    console.log(`Total time: ${this.formatTime(totalTime / 1000)}`);
    console.log(`Average rate: ${averageRate.toFixed(1)} items/sec`);
  }
}
```

## Best Practices

### 1. Choose Appropriate Batch Sizes

```typescript
// Consider these factors when choosing batch size:
const batchSizeGuidelines = {
  // Memory-intensive operations
  memoryIntensive: 10,
  
  // Network operations
  networkOperations: 50,
  
  // CPU-intensive operations
  cpuIntensive: 100,
  
  // Simple transformations
  simpleTransforms: 1000
};
```

### 2. Monitor Resource Usage

```typescript
// Monitor memory and CPU during batch processing
setInterval(() => {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();
  
  console.log('Resource usage:', {
    heapUsed: `${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB`,
    heapTotal: `${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB`,
    cpuUser: cpuUsage.user,
    cpuSystem: cpuUsage.system
  });
}, 10000); // Every 10 seconds
```

### 3. Implement Graceful Shutdown

```typescript
class GracefulBatchProcessor {
  private isShuttingDown = false;
  private activeBatches = new Set<Promise<any>>();
  
  constructor() {
    // Handle shutdown signals
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }
  
  async shutdown(): Promise<void> {
    console.log('Initiating graceful shutdown...');
    this.isShuttingDown = true;
    
    // Wait for active batches to complete
    if (this.activeBatches.size > 0) {
      console.log(`Waiting for ${this.activeBatches.size} active batches to complete...`);
      await Promise.all(this.activeBatches);
    }
    
    console.log('Graceful shutdown complete');
    process.exit(0);
  }
  
  async processBatch(batch: any[]): Promise<any[]> {
    if (this.isShuttingDown) {
      throw new Error('System is shutting down, cannot process new batches');
    }
    
    const batchPromise = this.performBatchWork(batch);
    this.activeBatches.add(batchPromise);
    
    try {
      const result = await batchPromise;
      return result;
    } finally {
      this.activeBatches.delete(batchPromise);
    }
  }
}
```

## Next Steps

- [Telemetry & Monitoring](./telemetry-monitoring.md) - Monitor streaming performance
- [Error Handling](./error-handling.md) - Handle errors in batch processing
- [Examples](./examples.md) - See streaming workflows in action
