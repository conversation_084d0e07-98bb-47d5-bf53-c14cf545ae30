/**
 * Test to verify the workflow diagram shows proper sequential flow
 */

import { WorkflowBuilder } from '../pangeaflow/pangeaflow';
import { generatePangeaFlowDiagram } from '../pangeaflow/utils/workflowDiagramGenerator';

async function testWorkflowFlow() {
  console.log('🔄 Testing workflow flow diagram generation...');

  try {
    // Create a workflow that demonstrates clear sequential flow
    const workflow = WorkflowBuilder.create()
      .addReasoningAgent('planner', async (prompt: string) => `Planning: ${prompt}`)
      .addReasoningAgent('analyzer', async (prompt: string) => `Analyzing: ${prompt}`)
      .addReasoningAgent('generator', async (prompt: string) => `Generating: ${prompt}`)
      .addReasoningAgent('reviewer', async (prompt: string) => `Reviewing: ${prompt}`)
      .addMemoryAgent('storage')
      .route('start', 'planner')
      .route('analyze', 'analyzer')
      .route('generate', 'generator')
      .route('review', 'reviewer')
      .route('complete', 'storage')
      .route('error', 'storage')
      .build();

    console.log('✅ Sequential workflow created');

    // Generate diagram showing the workflow flow
    await generatePangeaFlowDiagram(
      workflow,
      './output/sequential-workflow-flow.png',
      {
        width: 800,
        height: 1000,
        backgroundColor: '#f8f9fa',
        nodeColor: '#e3f2fd',
        edgeColor: '#1976d2',
        textColor: '#212121',
        fontSize: 14,
        showComponentDetails: true,
      }
    );

    console.log('✅ Sequential workflow diagram generated: ./output/sequential-workflow-flow.png');

    // Create a more complex workflow to test pattern recognition
    const complexWorkflow = WorkflowBuilder.create()
      .addReasoningAgent('repo-analyzer', async (prompt: string) => `Analyzing repo: ${prompt}`)
      .addReasoningAgent('concept-extractor', async (prompt: string) => `Extracting concepts: ${prompt}`)
      .addReasoningAgent('tutorial-planner', async (prompt: string) => `Planning tutorial: ${prompt}`)
      .addReasoningAgent('content-generator', async (prompt: string) => `Generating content: ${prompt}`)
      .addReasoningAgent('tutorial-assembler', async (prompt: string) => `Assembling tutorial: ${prompt}`)
      .addMemoryAgent('memory')
      .route('start', 'repo-analyzer')
      .route('extract-concepts', 'concept-extractor')
      .route('plan-tutorial', 'tutorial-planner')
      .route('generate-content', 'content-generator')
      .route('assemble-tutorial', 'tutorial-assembler')
      .route('complete', 'memory')
      .route('error', 'memory')
      .build();

    console.log('✅ Complex workflow created');

    // Generate diagram for complex workflow (should recognize Code2Tutor pattern)
    await generatePangeaFlowDiagram(
      complexWorkflow,
      './output/complex-workflow-flow.png',
      {
        width: 900,
        height: 1200,
        backgroundColor: '#ffffff',
        nodeColor: '#e8f5e9',
        edgeColor: '#388e3c',
        textColor: '#1b5e20',
        fontSize: 13,
        showComponentDetails: true,
      }
    );

    console.log('✅ Complex workflow diagram generated: ./output/complex-workflow-flow.png');

    console.log('\n🎉 Workflow flow diagrams generated successfully!');
    console.log('📁 Check the ./output/ directory for:');
    console.log('   - sequential-workflow-flow.png (simple sequential flow)');
    console.log('   - complex-workflow-flow.png (Code2Tutor-like pattern)');
    console.log('\n💡 These diagrams should show:');
    console.log('   • Vertical flow from top to bottom');
    console.log('   • Each action step as a node');
    console.log('   • Arrows showing the flow direction');
    console.log('   • Component details in each node');

  } catch (error) {
    console.error('❌ Error testing workflow flow:', error);
    if (error instanceof Error) {
      console.error('Stack:', error.stack);
    }
  }
}

// Run the test
testWorkflowFlow();
