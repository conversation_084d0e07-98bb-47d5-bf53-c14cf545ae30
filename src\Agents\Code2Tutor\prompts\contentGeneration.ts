
export const CONTENT_GENERATION_PROMPT = `\${language_instruction}Create an engaging tutorial section for the concept "\${concept_name}" in the project \${project_name}.

Section Details:
- Title: \${section_title}
- Target Audience: \${target_audience}
- Tutorial Format: \${tutorial_format}
- Estimated Time: \${estimated_time} minutes

Learning Goals:
\${learning_goals}

Key Points to Cover:
\${key_points}

Relevant Code Context:
\${code_context}

Previous Sections Context:
\${previous_context}

Create a comprehensive tutorial section that includes:

1. **Introduction** (2-3 paragraphs)
   - Hook the learner with a practical problem this concept solves
   - Explain why this concept is important
   - Preview what they'll learn and build

2. **Core Concept Explanation** (3-4 paragraphs)
   - Break down the concept into digestible parts
   - Use analogies and real-world examples
   - Connect to learner's existing knowledge

3. **Practical Examples** (3-5 code examples)
   - Start with simple, minimal examples
   - Build complexity gradually
   - Each example should be under 10 lines
   - Include expected output or behavior
   - Explain each example thoroughly

4. **Hands-on Exercise** (if \${include_exercises})
   - Practical coding challenge
   - Clear instructions and expected outcome
   - Hints for common issues
   - Solution explanation

5. **Deep Dive** (2-3 paragraphs)
   - How it works under the hood
   - Best practices and common patterns
   - When and why to use this concept

6. **Visual Aids** (if \${include_diagrams})
   - Mermaid diagrams to illustrate concepts
   - Keep diagrams simple and focused
   - Maximum 5 components per diagram

7. **Summary and Next Steps**
   - Recap key learnings
   - Connect to upcoming concepts
   - Encourage practice and exploration

Guidelines:
- Write in a friendly, encouraging tone
- Use "you" to address the learner directly
- Include plenty of code comments for clarity
- Break complex ideas into smaller chunks
- Provide context for why each step matters
- Use markdown formatting for readability

Output the complete tutorial section in Markdown format.`;