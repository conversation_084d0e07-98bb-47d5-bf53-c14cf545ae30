# Custom Agents

While PangeaFlow's built-in agents cover many use cases, you can create custom agents for specialized functionality. This guide shows how to build powerful, reusable custom agents that integrate seamlessly with PangeaFlow workflows.

## AgentComponent Base Class

All custom agents extend the `AgentComponent` base class:

```typescript
import { AgentComponent, ExecutionContext, ExecutionResult } from '@/pangeaflow';

abstract class AgentComponent {
  readonly id: ComponentId;
  protected readonly eventBus: EventBus;
  protected readonly telemetry: TelemetryCollector;
  protected readonly state: ReactiveState<Record<string, unknown>>;
  
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    initialState: Record<string, unknown> = {}
  );
  
  // Main execution method - implement this
  abstract execute(context: ExecutionContext): Promise<ExecutionResult>;
  
  // Emit events to other components
  protected emit<T>(type: string, payload: T, correlationId?: string): void;
  
  // Telemetry wrapper for operations
  protected async withTelemetry<T>(operation: string, fn: () => Promise<T>): Promise<T>;
}
```

## Basic Custom Agent

Here's a simple custom agent that processes text:

```typescript
class TextProcessorAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('text-processor', eventBus, telemetry, {
      processedCount: 0
    });
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const { text } = context.metadata as { text: string };
      
      // Emit status update
      this.emit('agent.status', {
        agentName: 'TextProcessor',
        status: 'processing',
        message: `Processing text of length ${text.length}`
      });
      
      // Process the text
      const result = await this.withTelemetry('text-processing', async () => {
        const processed = await this.processText(text);
        
        // Update state
        const currentState = this.state.get();
        this.state.set({
          ...currentState,
          processedCount: currentState.processedCount + 1
        });
        
        return processed;
      });
      
      // Emit completion event
      this.emit('text.processed', {
        originalLength: text.length,
        processedLength: result.length,
        processingTime: Date.now()
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { processedText: result }
      };
      
    } catch (error) {
      this.emit('agent.error', {
        agentName: 'TextProcessor',
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: { failedAt: 'text-processing' }
      };
    }
  }
  
  private async processText(text: string): Promise<string> {
    // Your text processing logic here
    return text.toLowerCase().trim();
  }
}
```

## Real-World Examples

### Repository Analysis Agent

Based on the Code2Tutor implementation:

```typescript
class RepoAnalysisAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('repo-analysis', eventBus, telemetry, {
      stage: 'repository-analysis',
      progress: 0
    });
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const shared = context.metadata as SharedStore;
    
    try {
      this.emit('agent.status', {
        agentName: 'RepoAnalysisAgent',
        status: 'starting',
        progress: 0,
        message: 'Starting repository analysis'
      });
      
      // Fetch repository files
      const files = await this.withTelemetry('fetch-files', async () => {
        if (shared.repo_url) {
          return await this.fetchGitHubFiles(shared);
        } else {
          return await this.fetchLocalFiles(shared);
        }
      });
      
      // Analyze repository structure
      const analysis = await this.withTelemetry('analyze-structure', async () => {
        return await this.analyzeRepoStructure(files);
      });
      
      // Update shared store
      shared.files = files;
      shared.repo_analysis = analysis;
      
      this.emit('agent.status', {
        agentName: 'RepoAnalysisAgent',
        status: 'completed',
        progress: 100,
        message: `Analyzed ${files.length} files`
      });
      
      return {
        success: true,
        data: { files, analysis },
        events: [],
        nextActions: ['extract-concepts'],
        metadata: shared
      };
      
    } catch (error) {
      this.emit('agent.error', {
        agentName: 'RepoAnalysisAgent',
        error: error.message,
        stage: 'repository-analysis'
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: shared
      };
    }
  }
  
  private async fetchGitHubFiles(shared: SharedStore) {
    // Implementation for fetching GitHub files
    return await fetch_selected_github_files(
      shared.repo_url!,
      shared.selected_files || [],
      {
        githubToken: shared.github_token,
        useRelativePaths: shared.use_relative_paths
      }
    );
  }
  
  private async fetchLocalFiles(shared: SharedStore) {
    // Implementation for fetching local files
    return await fetch_selected_local_files(
      shared.local_path!,
      shared.selected_files || []
    );
  }
  
  private async analyzeRepoStructure(files: any[]) {
    // Analyze repository structure
    return {
      totalFiles: files.length,
      languages: this.detectLanguages(files),
      structure: this.analyzeStructure(files)
    };
  }
  
  private detectLanguages(files: any[]): string[] {
    const extensions = files.map(f => f.name.split('.').pop()).filter(Boolean);
    return [...new Set(extensions)];
  }
  
  private analyzeStructure(files: any[]) {
    // Analyze directory structure, dependencies, etc.
    return {
      directories: this.getDirectories(files),
      entryPoints: this.findEntryPoints(files),
      dependencies: this.extractDependencies(files)
    };
  }
}
```

### Content Generation Agent

```typescript
class ContentGenerationAgent extends AgentComponent {
  private currentRetry = 0;
  private maxRetries = 3;
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('content-generation', eventBus, telemetry, {
      stage: 'content-generation',
      sectionsGenerated: 0
    });
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const shared = context.metadata as SharedStore;
    
    try {
      this.emit('agent.status', {
        agentName: 'ContentGenerationAgent',
        status: 'processing',
        progress: 0,
        message: 'Starting content generation'
      });
      
      const sections = await this.withTelemetry('generate-sections', async () => {
        return await this.generateTutorialSections(shared);
      });
      
      // Update shared store
      shared.tutorial_sections = sections;
      
      this.emit('agent.status', {
        agentName: 'ContentGenerationAgent',
        status: 'completed',
        progress: 100,
        message: `Generated ${sections.length} tutorial sections`
      });
      
      return {
        success: true,
        data: sections,
        events: [],
        nextActions: ['assemble-tutorial'],
        metadata: shared
      };
      
    } catch (error) {
      if (this.currentRetry < this.maxRetries) {
        this.currentRetry++;
        this.emit('agent.status', {
          agentName: 'ContentGenerationAgent',
          status: 'retrying',
          message: `Retry ${this.currentRetry}/${this.maxRetries}`
        });
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['generate-content'], // Retry same action
          metadata: shared
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: shared
      };
    }
  }
  
  private async generateTutorialSections(shared: SharedStore) {
    const { tutorial_plan, concepts } = shared;
    const sections = [];
    
    for (let i = 0; i < tutorial_plan.sections.length; i++) {
      const section = tutorial_plan.sections[i];
      
      this.emit('agent.status', {
        agentName: 'ContentGenerationAgent',
        status: 'processing',
        progress: (i / tutorial_plan.sections.length) * 100,
        message: `Generating section: ${section.title}`
      });
      
      const content = await this.generateSectionContent(section, concepts);
      sections.push(content);
      
      // Update state
      const currentState = this.state.get();
      this.state.set({
        ...currentState,
        sectionsGenerated: i + 1
      });
    }
    
    return sections;
  }
  
  private async generateSectionContent(section: any, concepts: any[]) {
    // Generate content using LLM
    const prompt = this.buildContentPrompt(section, concepts);
    const content = await callLlm_openrouter({
      prompt,
      model: "google/gemini-2.5-flash-preview-05-20",
      temperature: 0.7
    });
    
    return {
      ...section,
      content,
      generatedAt: new Date().toISOString()
    };
  }
}
```

## Advanced Patterns

### Stateful Agents

Agents that maintain complex state across executions:

```typescript
class StatefulProcessorAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('stateful-processor', eventBus, telemetry, {
      processingQueue: [],
      completedItems: [],
      currentBatch: null,
      statistics: {
        totalProcessed: 0,
        averageProcessingTime: 0,
        errorRate: 0
      }
    });
    
    // React to state changes
    this.state.subscribe((newState) => {
      this.emit('state.updated', {
        agentId: this.id,
        statistics: newState.statistics
      });
    });
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const currentState = this.state.get();
    const { action, data } = context.metadata as { action: string; data: any };
    
    switch (action) {
      case 'add-to-queue':
        return await this.addToQueue(data);
      case 'process-batch':
        return await this.processBatch();
      case 'get-statistics':
        return await this.getStatistics();
      default:
        throw new Error(`Unknown action: ${action}`);
    }
  }
  
  private async addToQueue(items: any[]): Promise<ExecutionResult> {
    const currentState = this.state.get();
    this.state.set({
      ...currentState,
      processingQueue: [...currentState.processingQueue, ...items]
    });
    
    return {
      success: true,
      data: { queueLength: currentState.processingQueue.length + items.length },
      events: [],
      nextActions: ['process-batch'],
      metadata: {}
    };
  }
  
  private async processBatch(): Promise<ExecutionResult> {
    const currentState = this.state.get();
    const batchSize = 10;
    const batch = currentState.processingQueue.slice(0, batchSize);
    
    if (batch.length === 0) {
      return {
        success: true,
        data: { message: 'No items to process' },
        events: [],
        nextActions: ['complete'],
        metadata: {}
      };
    }
    
    const startTime = Date.now();
    const processedItems = await this.processItems(batch);
    const processingTime = Date.now() - startTime;
    
    // Update state
    this.state.set({
      ...currentState,
      processingQueue: currentState.processingQueue.slice(batchSize),
      completedItems: [...currentState.completedItems, ...processedItems],
      currentBatch: null,
      statistics: this.updateStatistics(currentState.statistics, processedItems, processingTime)
    });
    
    return {
      success: true,
      data: { processedCount: processedItems.length },
      events: [],
      nextActions: currentState.processingQueue.length > batchSize ? ['process-batch'] : ['complete'],
      metadata: {}
    };
  }
}
```

### Event-Driven Agents

Agents that respond to external events:

```typescript
class EventDrivenAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('event-driven', eventBus, telemetry);
    
    // Subscribe to external events
    this.eventBus.on('data.received', this.handleDataReceived.bind(this));
    this.eventBus.on('user.action', this.handleUserAction.bind(this));
    this.eventBus.on('system.alert', this.handleSystemAlert.bind(this));
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // This agent primarily responds to events
    // The execute method might just set up listeners or return status
    
    return {
      success: true,
      data: { status: 'listening', listeners: 3 },
      events: [],
      nextActions: ['wait'],
      metadata: {}
    };
  }
  
  private async handleDataReceived(event: WorkflowEvent) {
    const { data } = event.payload as { data: any };
    
    // Process the received data
    const processed = await this.processData(data);
    
    // Emit result
    this.emit('data.processed', {
      originalData: data,
      processedData: processed,
      processedAt: Date.now()
    });
  }
  
  private async handleUserAction(event: WorkflowEvent) {
    const { action, userId } = event.payload as { action: string; userId: string };
    
    // Handle user action
    await this.handleAction(action, userId);
    
    this.emit('user.action.handled', {
      action,
      userId,
      handledAt: Date.now()
    });
  }
}
```

## Registration and Usage

### Manual Registration

Register custom agents directly with the orchestrator:

```typescript
const workflow = WorkflowBuilder.create();
const orchestrator = workflow['orchestrator'] as any;

// Create and register custom agent
const customAgent = new RepoAnalysisAgent(
  orchestrator.eventBus,
  orchestrator.telemetry
);
orchestrator.registerComponent(customAgent);

// Define routes
workflow
  .route('start', 'repo-analysis')
  .route('extract-concepts', 'concept-extraction')
  .build();
```

### Factory Pattern

Create reusable agent factories:

```typescript
class AgentFactory {
  static createRepoAnalysisAgent(eventBus: EventBus, telemetry: TelemetryCollector) {
    return new RepoAnalysisAgent(eventBus, telemetry);
  }
  
  static createContentGenerationAgent(eventBus: EventBus, telemetry: TelemetryCollector) {
    return new ContentGenerationAgent(eventBus, telemetry);
  }
}

// Usage
const repoAgent = AgentFactory.createRepoAnalysisAgent(eventBus, telemetry);
orchestrator.registerComponent(repoAgent);
```

## Best Practices

### Error Handling

Always implement comprehensive error handling:

```typescript
async execute(context: ExecutionContext): Promise<ExecutionResult> {
  try {
    // Main logic here
    return { success: true, /* ... */ };
  } catch (error) {
    // Log error
    console.error(`${this.id} execution failed:`, error);
    
    // Emit error event
    this.emit('agent.error', {
      agentName: this.id,
      error: error.message,
      context: context.metadata
    });
    
    // Return error result
    return {
      success: false,
      error: error as Error,
      events: [],
      nextActions: ['error'],
      metadata: context.metadata
    };
  }
}
```

### State Management

Use reactive state effectively:

```typescript
constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
  super('my-agent', eventBus, telemetry, {
    status: 'idle',
    progress: 0,
    lastUpdate: null
  });
  
  // React to state changes
  this.state.subscribe((newState) => {
    if (newState.status === 'completed') {
      this.emit('agent.completed', {
        agentId: this.id,
        completedAt: newState.lastUpdate
      });
    }
  });
}
```

### Telemetry Integration

Use telemetry for performance monitoring:

```typescript
async execute(context: ExecutionContext): Promise<ExecutionResult> {
  return await this.withTelemetry('main-execution', async () => {
    const step1 = await this.withTelemetry('step-1', () => this.performStep1());
    const step2 = await this.withTelemetry('step-2', () => this.performStep2(step1));
    return this.combineResults(step1, step2);
  });
}
```

## Next Steps

- [Event System](./event-system.md) - Learn about inter-agent communication
- [Telemetry & Monitoring](./telemetry-monitoring.md) - Monitor your custom agents
- [Examples](./examples.md) - See complete custom agent examples
