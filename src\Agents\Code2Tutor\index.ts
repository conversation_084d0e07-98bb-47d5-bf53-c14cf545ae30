// src/Agents/Code2Tutor/index.ts

/**
 * Code2Tutor Agent - Transform code into educational tutorials
 * 
 * This agent uses PangeaFlow architecture to create comprehensive, interactive
 * tutorials from code repositories. Unlike Code2Documentation which focuses on
 * technical documentation, Code2Tutor creates educational content designed for
 * learning and skill development.
 * 
 * Key Features:
 * - Educational content generation focused on learning outcomes
 * - Interactive exercises and coding challenges
 * - Progressive skill building with concept dependencies
 * - Multi-audience support (beginner, intermediate, advanced)
 * - Hands-on examples and practical applications
 * - Visual diagrams and explanations
 * 
 * Architecture:
 * - Uses PangeaFlow for reactive, event-driven workflow orchestration
 * - Modular agent components for each stage of tutorial creation
 * - Built-in telemetry and error recovery
 * - Streaming support for large repositories
 */

// Main workflow exports
export { 
  createCode2TutorFlow, 
  executeCode2TutorFlow,
  createDefaultSharedStore,
  validateSharedStore
} from './flow/pangeaFlow';

// Agent components
export * from './agents';

// Type definitions
export * from './types';

// Event management
export * from './utils/events';

// Prompt templates
export * from './prompts/conceptExtraction';
export * from './prompts/exerciseGeneration';

/**
 * Main entry point for the Code2Tutor agent
 * 
 * @example
 * ```typescript
 * import { executeCode2TutorFlow, createDefaultSharedStore } from '@/Agents/Code2Tutor';
 * 
 * const shared = createDefaultSharedStore({
 *   user_id: 'user123',
 *   repo_url: 'https://github.com/example/repo',
 *   project_name: 'My Project',
 *   target_audience: 'beginner',
 *   include_exercises: true
 * });
 * 
 * const results = await executeCode2TutorFlow(shared);
 * console.log('Tutorial created:', results);
 * ```
 */

// Version information
export const CODE2TUTOR_VERSION = '1.0.0';
export const SUPPORTED_LANGUAGES = [
  'JavaScript',
  'TypeScript', 
  'Python',
  'Java',
  'Go',
  'Rust',
  'C++',
  'C#',
  'PHP',
  'Ruby'
];

// Default configuration
export const DEFAULT_CONFIG = {
  MAX_CONCEPTS: 8,
  MIN_CONCEPTS: 3,
  DEFAULT_TARGET_AUDIENCE: 'beginner' as const,
  DEFAULT_TUTORIAL_FORMAT: 'guided' as const,
  DEFAULT_LANGUAGE: 'english',
  MAX_FILE_SIZE: 50000, // 50KB
  MAX_CONTEXT_LENGTH: 15000,
  DEFAULT_TEMPERATURE: 0.7,
  MAX_RETRIES: 3
};
