# Telemetry & Monitoring

PangeaFlow provides comprehensive built-in telemetry and monitoring capabilities that help you understand workflow performance, identify bottlenecks, and ensure reliable operation.

## Overview

PangeaFlow's telemetry system automatically collects:
- **Performance Metrics**: Execution times, success rates, throughput
- **Component Statistics**: Per-agent performance and behavior
- **Event Tracking**: Complete audit trail of workflow events
- **Error Analytics**: Detailed error patterns and recovery metrics
- **Resource Usage**: Memory and CPU utilization patterns

## TelemetryCollector

The core telemetry system that automatically tracks all workflow operations.

### Basic Usage

```typescript
// Telemetry is automatically available in all agents
class MyAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // Automatic telemetry collection with withTelemetry
    const result = await this.withTelemetry('main-operation', async () => {
      return await this.performWork();
    });
    
    // Manual telemetry recording
    this.telemetry.record({
      componentId: this.id,
      operation: 'custom-metric',
      duration: 150,
      success: true,
      metadata: { itemsProcessed: 42 }
    });
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions: ['continue'],
      metadata: {}
    };
  }
}
```

### Telemetry Data Structure

```typescript
interface TelemetryData {
  componentId: ComponentId;        // Which component performed the operation
  operation: string;               // Operation name
  duration: number;                // Execution time in milliseconds
  success: boolean;                // Whether operation succeeded
  metadata: Record<string, unknown>; // Additional context data
}
```

## Performance Metrics

### Workflow-Level Metrics

```typescript
// Get comprehensive workflow metrics
const metrics = workflow.getMetrics();

console.log('Workflow Performance:', {
  totalExecutions: metrics.totalExecutions,
  averageDuration: metrics.averageDuration,
  successRate: metrics.successRate,
  errorRate: metrics.errorRate,
  throughput: metrics.throughput // operations per second
});
```

### Component-Level Metrics

```typescript
// Get per-component statistics
const componentStats = metrics.componentStats;

Object.entries(componentStats).forEach(([componentId, stats]) => {
  console.log(`Component ${componentId}:`, {
    executions: stats.executions,
    averageDuration: stats.averageDuration,
    successRate: stats.successRate,
    lastExecution: stats.lastExecution
  });
});
```

### Real-Time Monitoring

```typescript
// Monitor performance in real-time
workflow.on('step.completed', (event) => {
  const currentMetrics = workflow.getMetrics();
  
  // Check for performance degradation
  if (currentMetrics.averageDuration > 5000) { // 5 seconds
    console.warn('Performance degradation detected:', {
      currentAverage: currentMetrics.averageDuration,
      component: event.source
    });
  }
  
  // Monitor success rate
  if (currentMetrics.successRate < 0.95) { // Below 95%
    console.error('Success rate below threshold:', {
      currentRate: currentMetrics.successRate,
      totalExecutions: currentMetrics.totalExecutions
    });
  }
});
```

## Custom Metrics

### Operation-Specific Metrics

```typescript
class DataProcessingAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const { dataset } = context.metadata;
    
    // Track data processing metrics
    const processingResult = await this.withTelemetry('data-processing', async () => {
      const startTime = Date.now();
      const processed = await this.processDataset(dataset);
      
      // Record custom metrics
      this.telemetry.record({
        componentId: this.id,
        operation: 'dataset-processing',
        duration: Date.now() - startTime,
        success: true,
        metadata: {
          inputSize: dataset.length,
          outputSize: processed.length,
          processingRate: processed.length / ((Date.now() - startTime) / 1000),
          memoryUsage: process.memoryUsage().heapUsed
        }
      });
      
      return processed;
    });
    
    return {
      success: true,
      data: processingResult,
      events: [],
      nextActions: ['continue'],
      metadata: { ...context.metadata, processed: processingResult }
    };
  }
}
```

### Business Metrics

```typescript
class BusinessMetricsAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const result = await this.performBusinessLogic(context.metadata);
    
    // Track business-specific metrics
    this.telemetry.record({
      componentId: this.id,
      operation: 'business-transaction',
      duration: result.processingTime,
      success: result.success,
      metadata: {
        transactionType: result.type,
        customerSegment: result.customerSegment,
        revenue: result.revenue,
        conversionRate: result.conversionRate
      }
    });
    
    // Emit business event for external monitoring
    this.emit('business.transaction.completed', {
      type: result.type,
      value: result.revenue,
      timestamp: Date.now()
    });
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions: ['continue'],
      metadata: {}
    };
  }
}
```

## Error Tracking

### Automatic Error Collection

```typescript
// Errors are automatically tracked when agents fail
class ErrorProneAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.withTelemetry('risky-operation', async () => {
        return await this.performRiskyOperation();
      });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: {}
      };
    } catch (error) {
      // Error is automatically recorded in telemetry
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: { errorContext: 'risky-operation' }
      };
    }
  }
}
```

### Error Pattern Analysis

```typescript
// Analyze error patterns from telemetry data
function analyzeErrorPatterns(metrics: TelemetryStats) {
  const errorsByComponent = {};
  const errorsByOperation = {};
  
  metrics.componentStats.forEach((stats, componentId) => {
    if (stats.errorRate > 0) {
      errorsByComponent[componentId] = {
        errorRate: stats.errorRate,
        totalErrors: stats.executions * stats.errorRate,
        commonErrors: stats.commonErrors
      };
    }
  });
  
  return {
    errorsByComponent,
    errorsByOperation,
    overallErrorRate: metrics.errorRate,
    recommendations: generateErrorRecommendations(errorsByComponent)
  };
}

function generateErrorRecommendations(errorsByComponent: any) {
  const recommendations = [];
  
  Object.entries(errorsByComponent).forEach(([componentId, stats]: [string, any]) => {
    if (stats.errorRate > 0.1) { // 10% error rate
      recommendations.push({
        component: componentId,
        issue: 'High error rate',
        suggestion: 'Review error handling and add retry logic',
        priority: 'high'
      });
    }
  });
  
  return recommendations;
}
```

## Performance Monitoring

### Latency Tracking

```typescript
class LatencyMonitoringAgent extends AgentComponent {
  private latencyThresholds = {
    warning: 1000,  // 1 second
    critical: 5000  // 5 seconds
  };
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const startTime = Date.now();
    
    const result = await this.withTelemetry('monitored-operation', async () => {
      return await this.performOperation();
    });
    
    const duration = Date.now() - startTime;
    
    // Check latency thresholds
    if (duration > this.latencyThresholds.critical) {
      this.emit('performance.critical', {
        component: this.id,
        operation: 'monitored-operation',
        duration,
        threshold: this.latencyThresholds.critical
      });
    } else if (duration > this.latencyThresholds.warning) {
      this.emit('performance.warning', {
        component: this.id,
        operation: 'monitored-operation',
        duration,
        threshold: this.latencyThresholds.warning
      });
    }
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions: ['continue'],
      metadata: {}
    };
  }
}
```

### Throughput Monitoring

```typescript
class ThroughputMonitor {
  private processedItems = 0;
  private startTime = Date.now();
  private intervalId: NodeJS.Timeout;
  
  constructor(private workflow: WorkflowOrchestrator) {
    // Monitor throughput every 30 seconds
    this.intervalId = setInterval(() => {
      this.reportThroughput();
    }, 30000);
    
    // Track completed operations
    workflow.on('step.completed', () => {
      this.processedItems++;
    });
  }
  
  private reportThroughput() {
    const elapsed = (Date.now() - this.startTime) / 1000; // seconds
    const throughput = this.processedItems / elapsed;
    
    console.log('Throughput Report:', {
      itemsProcessed: this.processedItems,
      elapsedTime: elapsed,
      throughput: throughput.toFixed(2) + ' items/second',
      timestamp: new Date().toISOString()
    });
    
    // Reset counters
    this.processedItems = 0;
    this.startTime = Date.now();
  }
  
  cleanup() {
    clearInterval(this.intervalId);
  }
}
```

## Resource Monitoring

### Memory Usage Tracking

```typescript
class ResourceMonitoringAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const memoryBefore = process.memoryUsage();
    
    const result = await this.withTelemetry('resource-intensive-operation', async () => {
      return await this.performResourceIntensiveWork();
    });
    
    const memoryAfter = process.memoryUsage();
    const memoryDelta = memoryAfter.heapUsed - memoryBefore.heapUsed;
    
    // Track memory usage
    this.telemetry.record({
      componentId: this.id,
      operation: 'memory-usage',
      duration: 0,
      success: true,
      metadata: {
        memoryBefore: memoryBefore.heapUsed,
        memoryAfter: memoryAfter.heapUsed,
        memoryDelta,
        heapTotal: memoryAfter.heapTotal,
        external: memoryAfter.external
      }
    });
    
    // Alert on high memory usage
    if (memoryAfter.heapUsed > 500 * 1024 * 1024) { // 500MB
      this.emit('resource.memory.high', {
        component: this.id,
        heapUsed: memoryAfter.heapUsed,
        heapTotal: memoryAfter.heapTotal
      });
    }
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions: ['continue'],
      metadata: {}
    };
  }
}
```

## External Monitoring Integration

### Prometheus Metrics

```typescript
// Integration with Prometheus (example)
class PrometheusIntegration {
  private metrics = {
    workflowExecutions: new Counter({
      name: 'pangeaflow_workflow_executions_total',
      help: 'Total number of workflow executions',
      labelNames: ['workflow_id', 'status']
    }),
    
    operationDuration: new Histogram({
      name: 'pangeaflow_operation_duration_seconds',
      help: 'Duration of workflow operations',
      labelNames: ['component_id', 'operation'],
      buckets: [0.1, 0.5, 1, 2, 5, 10]
    })
  };
  
  constructor(workflow: WorkflowOrchestrator) {
    workflow.on('workflow.completed', (event) => {
      this.metrics.workflowExecutions
        .labels(event.source, 'success')
        .inc();
    });
    
    workflow.on('workflow.failed', (event) => {
      this.metrics.workflowExecutions
        .labels(event.source, 'failure')
        .inc();
    });
    
    workflow.on('step.completed', (event) => {
      const { duration, componentId, operation } = event.payload;
      this.metrics.operationDuration
        .labels(componentId, operation)
        .observe(duration / 1000); // Convert to seconds
    });
  }
}
```

### Custom Dashboard Integration

```typescript
// Send metrics to custom dashboard
class DashboardIntegration {
  constructor(private dashboardUrl: string, private workflow: WorkflowOrchestrator) {
    this.setupMetricsReporting();
  }
  
  private setupMetricsReporting() {
    // Send metrics every minute
    setInterval(() => {
      this.sendMetrics();
    }, 60000);
    
    // Send real-time events
    this.workflow.on('agent.status', (event) => {
      this.sendRealTimeUpdate(event);
    });
  }
  
  private async sendMetrics() {
    const metrics = this.workflow.getMetrics();
    
    try {
      await fetch(`${this.dashboardUrl}/api/metrics`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: Date.now(),
          metrics: {
            totalExecutions: metrics.totalExecutions,
            averageDuration: metrics.averageDuration,
            successRate: metrics.successRate,
            componentStats: metrics.componentStats
          }
        })
      });
    } catch (error) {
      console.error('Failed to send metrics to dashboard:', error);
    }
  }
  
  private async sendRealTimeUpdate(event: any) {
    try {
      await fetch(`${this.dashboardUrl}/api/events`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          timestamp: event.timestamp,
          type: event.type,
          source: event.source,
          payload: event.payload
        })
      });
    } catch (error) {
      console.error('Failed to send real-time update:', error);
    }
  }
}
```

## Alerting

### Threshold-Based Alerts

```typescript
class AlertingSystem {
  private thresholds = {
    errorRate: 0.05,        // 5%
    averageDuration: 10000, // 10 seconds
    memoryUsage: 1024 * 1024 * 1024 // 1GB
  };
  
  constructor(workflow: WorkflowOrchestrator) {
    // Check metrics periodically
    setInterval(() => {
      this.checkThresholds(workflow.getMetrics());
    }, 30000); // Every 30 seconds
  }
  
  private checkThresholds(metrics: any) {
    // Error rate alert
    if (metrics.errorRate > this.thresholds.errorRate) {
      this.sendAlert('high_error_rate', {
        currentRate: metrics.errorRate,
        threshold: this.thresholds.errorRate,
        severity: 'warning'
      });
    }
    
    // Performance alert
    if (metrics.averageDuration > this.thresholds.averageDuration) {
      this.sendAlert('slow_performance', {
        currentDuration: metrics.averageDuration,
        threshold: this.thresholds.averageDuration,
        severity: 'warning'
      });
    }
    
    // Memory alert
    const memoryUsage = process.memoryUsage().heapUsed;
    if (memoryUsage > this.thresholds.memoryUsage) {
      this.sendAlert('high_memory_usage', {
        currentUsage: memoryUsage,
        threshold: this.thresholds.memoryUsage,
        severity: 'critical'
      });
    }
  }
  
  private async sendAlert(type: string, data: any) {
    console.error(`ALERT [${type}]:`, data);
    
    // Send to external alerting system
    try {
      await fetch('https://alerts.example.com/webhook', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type,
          data,
          timestamp: Date.now(),
          source: 'pangeaflow'
        })
      });
    } catch (error) {
      console.error('Failed to send alert:', error);
    }
  }
}
```

## Best Practices

### 1. Meaningful Operation Names

```typescript
// Good - descriptive operation names
await this.withTelemetry('llm-content-generation', async () => {
  return await this.generateContent();
});

await this.withTelemetry('file-validation', async () => {
  return await this.validateFiles();
});

// Avoid - generic names
await this.withTelemetry('work', async () => {
  return await this.doStuff();
});
```

### 2. Contextual Metadata

```typescript
// Include relevant context in telemetry
this.telemetry.record({
  componentId: this.id,
  operation: 'data-processing',
  duration: processingTime,
  success: true,
  metadata: {
    inputSize: data.length,
    outputSize: result.length,
    processingMode: 'batch',
    dataType: 'user-content',
    userId: context.metadata.userId
  }
});
```

### 3. Performance Budgets

```typescript
// Set and monitor performance budgets
class PerformanceBudgetMonitor {
  private budgets = {
    'llm-call': 5000,      // 5 seconds
    'file-processing': 1000, // 1 second
    'data-validation': 500   // 500ms
  };
  
  checkBudget(operation: string, duration: number) {
    const budget = this.budgets[operation];
    if (budget && duration > budget) {
      console.warn(`Performance budget exceeded for ${operation}:`, {
        actual: duration,
        budget,
        overage: duration - budget
      });
    }
  }
}
```

## Next Steps

- [Error Handling](./error-handling.md) - Comprehensive error handling strategies
- [Examples](./examples.md) - See monitoring in action
- [Troubleshooting](./troubleshooting.md) - Debug performance issues
