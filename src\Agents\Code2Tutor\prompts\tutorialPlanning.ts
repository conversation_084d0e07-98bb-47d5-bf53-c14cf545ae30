
export const TUTORIAL_PLANNING_PROMPT = `For the project \${project_name}:

Learning Concepts Identified:
\${concepts_yaml}

Target Audience: \${target_audience}
Tutorial Format: \${tutorial_format}
Include Exercises: \${include_exercises}
Include Diagrams: \${include_diagrams}

\${language_instruction}Create a comprehensive tutorial structure that teaches these concepts effectively.

Design a tutorial that:
1. Follows a logical learning progression
2. Builds skills incrementally
3. Includes practical, hands-on learning
4. Is appropriate for \${target_audience} learners

For the tutorial structure, provide:

1. **Tutorial Metadata:**
   - Title (engaging and descriptive)
   - Description (what learners will achieve)
   - Estimated completion time in minutes
   - Learning objectives (3-5 specific skills/knowledge)
   - Prerequisites (external knowledge needed)

2. **Learning Progression:**
   - Order the concepts for optimal learning flow
   - Identify concept relationships (prerequisite, builds-on, related)
   - Plan how concepts connect and reinforce each other

3. **Section Planning:**
   For each concept, plan a tutorial section with:
   - Section title and learning goals
   - Key points to cover
   - Code examples to include
   - Exercises/activities (if enabled)
   - Estimated time for this section

Format as YAML:

\`\`\`yaml
tutorial_metadata:
  title: "Tutorial Title"
  description: "What learners will accomplish..."
  estimated_time: 120
  target_audience: "\${target_audience}"
  learning_objectives:
    - "Specific skill 1"
    - "Specific skill 2"
  prerequisites:
    - "External knowledge needed"

concept_relationships:
  - from: "Concept A"
    to: "Concept B"
    type: "prerequisite"
    strength: 0.9

progression_path:
  - "concept_1_id"
  - "concept_2_id"

sections:
  - id: "section_1"
    concept: "Concept Name"
    title: "Section Title"
    learning_goals:
      - "What learners will understand"
    key_points:
      - "Important point 1"
      - "Important point 2"
    code_examples:
      - "Brief description of example 1"
    exercises:
      - "Exercise description"
    estimated_time: 25
\`\`\``;


