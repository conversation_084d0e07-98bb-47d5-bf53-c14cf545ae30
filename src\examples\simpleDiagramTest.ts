/**
 * Simple test to verify the PangeaFlow diagram generator works
 */

import { WorkflowBuilder } from '../pangeaflow/pangeaflow';
import { generatePangeaFlowDiagram } from '../pangeaflow/utils/flowDiagramGenerator';

async function simpleTest() {
  console.log('🧪 Testing PangeaFlow diagram generator...');

  try {
    // Create a simple workflow
    const workflow = WorkflowBuilder.create()
      .addReasoningAgent('planner', async (prompt: string) => `Planning: ${prompt}`)
      .addMemoryAgent('memory')
      .route('start', 'planner')
      .route('store', 'memory')
      .build();

    console.log('✅ Workflow created');

    // Generate diagram
    await generatePangeaFlowDiagram(
      workflow,
      './output/simple-test.png',
      {
        width: 800,
        height: 400,
        backgroundColor: '#ffffff',
        componentColor: '#e3f2fd',
        actionColor: '#e8f5e9',
        edgeColor: '#1976d2',
        textColor: '#212121',
        fontSize: 12,
      }
    );

    console.log('✅ Diagram generated: ./output/simple-test.png');

  } catch (error) {
    console.error('❌ Error:', error);
    if (error instanceof Error) {
      console.error('Stack:', error.stack);
    }
  }
}

simpleTest();
