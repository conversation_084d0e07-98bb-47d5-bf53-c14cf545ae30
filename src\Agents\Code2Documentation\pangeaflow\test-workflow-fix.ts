// Test script to verify workflow routing fixes

import { executeCode2DocumentationWorkflow, createDefaultSharedStore } from './flow/pangeaFlow';

/**
 * Test basic workflow execution with minimal data
 */
async function testWorkflowRouting() {
  console.log('🧪 Testing Code2Documentation PangeaFlow workflow routing...');

  try {
    // Create minimal test data
    const testShared = createDefaultSharedStore({
      user_id: 'test-user',
      selected_files: [
        ['test.js', 'console.log("Hello World");'],
        ['utils.js', 'export function add(a, b) { return a + b; }']
      ] as [string, string][],
      language: 'english',
      max_abstraction_num: 2,
      project_name: 'Test Project',
      tutorial_id: 'test-tutorial-123'
    });

    console.log('🔄 Executing workflow with test data...');
    console.log('📋 Test shared store:', JSON.stringify(testShared, null, 2));

    const result = await executeCode2DocumentationWorkflow(testShared);

    console.log('📊 Workflow result:', result);

    if (result.success) {
      console.log('✅ Workflow completed successfully');
      console.log('📋 Result data keys:', Object.keys(result.data || {}));
    } else {
      console.log('❌ Workflow failed:', result.error?.message);
      console.log('📋 Error stack:', result.error?.stack);
    }

  } catch (error) {
    console.log('❌ Test threw error:', error);
    console.log('📋 Error stack:', (error as Error).stack);
  }

  console.log('🏁 Workflow routing test completed');
}

export { testWorkflowRouting };

// Run test immediately
testWorkflowRouting().catch(console.error);
