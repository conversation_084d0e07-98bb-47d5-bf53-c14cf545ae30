// Simple validation script to check if the fixes are working
console.log('🔍 Validating Code2Documentation fixes...');

// Test 1: Check if WriteChaptersAgent can be imported and instantiated
try {
  console.log('📦 Testing WriteChaptersAgent import...');
  
  // Import the agent
  import('./agents/WriteChaptersAgent.js').then(async (module) => {
    const { WriteChaptersAgent } = module;
    
    if (!WriteChaptersAgent) {
      console.error('❌ WriteChaptersAgent not found in module');
      return;
    }
    
    console.log('✅ WriteChaptersAgent imported successfully');
    
    // Test instantiation
    const mockEventBus = { on: () => {}, emit: () => {} };
    const mockTelemetry = { record: () => {} };
    
    try {
      const agent = new WriteChaptersAgent(mockEventBus, mockTelemetry, 3);
      console.log('✅ WriteChaptersAgent instantiated successfully');
      
      // Test that it has the execute method
      if (typeof agent.execute === 'function') {
        console.log('✅ WriteChaptersAgent has execute method');
      } else {
        console.error('❌ WriteChaptersAgent missing execute method');
      }
      
    } catch (error) {
      console.error('❌ Failed to instantiate WriteChaptersAgent:', error.message);
    }
    
  }).catch((error) => {
    console.error('❌ Failed to import WriteChaptersAgent:', error.message);
  });
  
} catch (error) {
  console.error('❌ Import test failed:', error.message);
}

// Test 2: Check if CombineTutorialAgent can be imported and instantiated
try {
  console.log('📦 Testing CombineTutorialAgent import...');
  
  // Import the agent
  import('./agents/CombineTutorialAgent.js').then(async (module) => {
    const { CombineTutorialAgent } = module;
    
    if (!CombineTutorialAgent) {
      console.error('❌ CombineTutorialAgent not found in module');
      return;
    }
    
    console.log('✅ CombineTutorialAgent imported successfully');
    
    // Test instantiation
    const mockEventBus = { on: () => {}, emit: () => {} };
    const mockTelemetry = { record: () => {} };
    
    try {
      const agent = new CombineTutorialAgent(mockEventBus, mockTelemetry);
      console.log('✅ CombineTutorialAgent instantiated successfully');
      
      // Test that it has the execute method
      if (typeof agent.execute === 'function') {
        console.log('✅ CombineTutorialAgent has execute method');
      } else {
        console.error('❌ CombineTutorialAgent missing execute method');
      }
      
    } catch (error) {
      console.error('❌ Failed to instantiate CombineTutorialAgent:', error.message);
    }
    
  }).catch((error) => {
    console.error('❌ Failed to import CombineTutorialAgent:', error.message);
  });
  
} catch (error) {
  console.error('❌ Import test failed:', error.message);
}

// Test 3: Check if workflow can be created
try {
  console.log('📦 Testing workflow creation...');
  
  import('./flow/pangeaFlow.js').then(async (module) => {
    const { createCode2DocumentationWorkflow } = module;
    
    if (!createCode2DocumentationWorkflow) {
      console.error('❌ createCode2DocumentationWorkflow not found in module');
      return;
    }
    
    console.log('✅ createCode2DocumentationWorkflow imported successfully');
    
    try {
      const workflow = createCode2DocumentationWorkflow();
      console.log('✅ Workflow created successfully');
      
      if (workflow && typeof workflow.execute === 'function') {
        console.log('✅ Workflow has execute method');
      } else {
        console.error('❌ Workflow missing execute method');
      }
      
    } catch (error) {
      console.error('❌ Failed to create workflow:', error.message);
    }
    
  }).catch((error) => {
    console.error('❌ Failed to import workflow:', error.message);
  });
  
} catch (error) {
  console.error('❌ Workflow test failed:', error.message);
}

console.log('🏁 Validation tests initiated');

// Give some time for async operations to complete
setTimeout(() => {
  console.log('⏰ Validation complete - check results above');
  process.exit(0);
}, 5000);
