// src/Agents/Code2Tutor/agents/RepoAnalysisAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow/pangeaflow';
import { SharedStore } from '../types';
import { emitAgentStatus, emitTutorProgress, emitTutorError } from '../utils/events';
import { fetch_selected_github_files } from '../../Code2Documentation/utils/crawl_github_files';
import { fetch_selected_local_files } from '../../Code2Documentation/utils/crawl_local_files';

/**
 * RepoAnalysisAgent - Analyzes repository structure and fetches relevant files
 * 
 * This agent is responsible for:
 * - Fetching files from GitHub repositories or local directories
 * - Filtering files based on include/exclude patterns
 * - Analyzing repository structure for educational content
 * - Preparing file context for concept extraction
 */
export class RepoAnalysisAgent extends AgentComponent {
  constructor(eventBus: any, telemetry: any) {
    super('repo-analysis', eventBus, telemetry, {
      stage: 'repository-analysis',
      progress: 0
    });
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('repo-analysis', async () => {
      console.log('🔍 RepoAnalysisAgent: Starting execution');
      emitAgentStatus('RepoAnalysisAgent', 'starting', 0, 'Initializing repository analysis');

      const shared = context.sharedState.shared as SharedStore;

      try {
        console.log('🔍 RepoAnalysisAgent: Validating inputs', {
          repo_url: shared.repo_url,
          local_dir: shared.local_dir,
          selected_files: shared.selected_files?.length || 0
        });

        // Validate required inputs
        if (!shared.repo_url && !shared.local_dir) {
          const error = new Error('Either repo_url or local_dir must be provided');
          console.error('❌ RepoAnalysisAgent: Validation failed:', error.message);
          emitTutorError(error, 'repo-analysis-validation');
          throw error;
        }

        emitAgentStatus('RepoAnalysisAgent', 'processing', 10, 'Fetching repository files');
        emitTutorProgress('Repository Analysis', 10, 'Starting file retrieval');

        let files: [string, string][] = [];

        if (shared.repo_url) {
          // Fetch from GitHub repository
          console.log('🔍 RepoAnalysisAgent: Fetching from GitHub:', shared.repo_url);
          emitAgentStatus('RepoAnalysisAgent', 'processing', 20, 'Fetching files from GitHub repository');

          try {
            const result = await fetch_selected_github_files(
              shared.repo_url,
              shared.selected_files || [],
              {
                githubToken: shared.github_token,
                useRelativePaths: true
              }
            );

            console.log('🔍 RepoAnalysisAgent: GitHub fetch result:', {
              success: !!result,
              filesCount: result?.files ? Object.keys(result.files).length : 0
            });

            if (!result || !result.files) {
              const error = new Error('Failed to fetch files from GitHub repository');
              console.error('❌ RepoAnalysisAgent: GitHub fetch failed:', error.message);
              emitTutorError(error, 'github-fetch');
              throw error;
            }

            files = Object.entries(result.files || {});
          } catch (fetchError) {
            console.error('❌ RepoAnalysisAgent: GitHub fetch error:', fetchError);
            emitTutorError(fetchError as Error, 'github-fetch');
            throw fetchError;
          }
        } else if (shared.local_dir) {
          // Fetch from local directory
          console.log('🔍 RepoAnalysisAgent: Fetching from local directory:', shared.local_dir);
          emitAgentStatus('RepoAnalysisAgent', 'processing', 20, 'Fetching files from local directory');

          try {
            const result = await fetch_selected_local_files(
              shared.local_dir,
              shared.selected_files || [],
              {
                useRelativePaths: true
              }
            );

            console.log('🔍 RepoAnalysisAgent: Local fetch result:', {
              success: !!result,
              filesCount: result?.files ? Object.keys(result.files).length : 0
            });

            if (!result || !result.files) {
              const error = new Error('Failed to fetch files from local directory');
              console.error('❌ RepoAnalysisAgent: Local fetch failed:', error.message);
              emitTutorError(error, 'local-fetch');
              throw error;
            }

            files = Object.entries(result.files || {});
          } catch (fetchError) {
            console.error('❌ RepoAnalysisAgent: Local fetch error:', fetchError);
            emitTutorError(fetchError as Error, 'local-fetch');
            throw fetchError;
          }
        }

        emitAgentStatus('RepoAnalysisAgent', 'processing', 60, `Retrieved ${files.length} files`);
        emitTutorProgress('Repository Analysis', 60, `Analyzed ${files.length} files`);

        // Filter files for educational relevance
        const educationalFiles = this.filterEducationalFiles(files);
        
        emitAgentStatus('RepoAnalysisAgent', 'processing', 80, `Filtered to ${educationalFiles.length} educational files`);

        // Analyze repository structure
        const repoStructure = this.analyzeRepoStructure(educationalFiles);
        
        emitAgentStatus('RepoAnalysisAgent', 'processing', 90, 'Analyzing repository structure');

        // Update shared store
        shared.files = educationalFiles;
        
        emitAgentStatus('RepoAnalysisAgent', 'completed', 100, 'Repository analysis completed');
        emitTutorProgress('Repository Analysis', 100, `Ready for concept extraction with ${educationalFiles.length} files`);

        this.emit('repo.analyzed', {
          fileCount: educationalFiles.length,
          structure: repoStructure,
          languages: this.detectLanguages(educationalFiles)
        }, context.id);

        return {
          success: true,
          output: {
            files: educationalFiles,
            structure: repoStructure,
            fileCount: educationalFiles.length
          },
          events: [],
          nextActions: ['extract-concepts'],
          sharedStateUpdates: {
            // ✅ Preserve existing shared state and add new data
            ...context.sharedState,
            files: educationalFiles,
            primaryLanguage: this.detectPrimaryLanguage(educationalFiles),
            // Ensure project_name is set if not already present
            project_name: shared.project_name || this.extractProjectName(shared.repo_url || shared.local_dir || 'unknown-project')
          }
        };

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('💥 RepoAnalysisAgent: Execution failed:', error);

        emitAgentStatus('RepoAnalysisAgent', 'error', 0, `Error: ${errorMessage}`);
        emitTutorError(error instanceof Error ? error : new Error(errorMessage), 'repo-analysis', {
          shared: {
            repo_url: shared?.repo_url,
            local_dir: shared?.local_dir,
            selected_files: shared?.selected_files?.length || 0
          }
        });

        return {
          success: false,
          error: error instanceof Error ? error : new Error(errorMessage),
          events: [],
          nextActions: ['error'],
          sharedStateUpdates: {
            stage: 'repo-analysis',
            errorType: error instanceof Error ? error.constructor.name : 'UnknownError',
            errorMessage
          }
        };
      }
    });
  }

  /**
   * Filter files to focus on those most relevant for educational content
   */
  private filterEducationalFiles(files: [string, string][]): [string, string][] {
    const educationalPatterns = [
      /\.(js|jsx|ts|tsx|py|java|go|rs|cpp|c|h)$/i, // Source code files
      /\.(md|rst|txt)$/i, // Documentation files
      /^(README|CONTRIBUTING|CHANGELOG)/i, // Important docs
      /\.(json|yaml|yml)$/i, // Configuration files
      /Dockerfile$/i, // Docker files
      /Makefile$/i // Build files
    ];

    const excludePatterns = [
      /node_modules/,
      /\.git/,
      /dist/,
      /build/,
      /coverage/,
      /\.test\./,
      /\.spec\./,
      /test/,
      /tests/,
      /\.min\./,
      /\.bundle\./
    ];

    return files.filter(([path, content]) => {
      // Check if file should be excluded
      if (excludePatterns.some(pattern => pattern.test(path))) {
        return false;
      }

      // Check if file matches educational patterns
      if (educationalPatterns.some(pattern => pattern.test(path))) {
        return true;
      }

      // Include files with substantial content (likely to be educational)
      return content.length > 100 && content.length < 10000;
    });
  }

  /**
   * Analyze repository structure to understand project organization
   */
  private analyzeRepoStructure(files: [string, string][]): any {
    const structure = {
      directories: new Set<string>(),
      fileTypes: new Map<string, number>(),
      totalFiles: files.length,
      avgFileSize: 0
    };

    let totalSize = 0;

    files.forEach(([path, content]) => {
      // Extract directory
      const dir = path.substring(0, path.lastIndexOf('/'));
      if (dir) structure.directories.add(dir);

      // Extract file extension
      const ext = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
      structure.fileTypes.set(ext, (structure.fileTypes.get(ext) || 0) + 1);

      totalSize += content.length;
    });

    structure.avgFileSize = Math.round(totalSize / files.length);

    return {
      directories: Array.from(structure.directories),
      fileTypes: Object.fromEntries(structure.fileTypes),
      totalFiles: structure.totalFiles,
      avgFileSize: structure.avgFileSize
    };
  }

  /**
   * Detect programming languages used in the repository
   */
  private detectLanguages(files: [string, string][]): string[] {
    const languageMap: Record<string, string> = {
      'js': 'JavaScript',
      'jsx': 'JavaScript',
      'ts': 'TypeScript',
      'tsx': 'TypeScript',
      'py': 'Python',
      'java': 'Java',
      'go': 'Go',
      'rs': 'Rust',
      'cpp': 'C++',
      'c': 'C',
      'h': 'C/C++',
      'php': 'PHP',
      'rb': 'Ruby',
      'cs': 'C#'
    };

    const languages = new Set<string>();
    
    files.forEach(([path]) => {
      const ext = path.substring(path.lastIndexOf('.') + 1).toLowerCase();
      if (languageMap[ext]) {
        languages.add(languageMap[ext]);
      }
    });

    return Array.from(languages);
  }

  /**
   * Detect the primary programming language
   */
  private detectPrimaryLanguage(files: [string, string][]): string {
    const languages = this.detectLanguages(files);
    return languages[0] || 'Unknown';
  }

  /**
   * Extract project name from repository URL or local directory
   */
  private extractProjectName(repoUrlOrPath: string): string {
    try {
      // Handle GitHub URLs
      if (repoUrlOrPath.includes('github.com')) {
        const match = repoUrlOrPath.match(/github\.com\/[^\/]+\/([^\/\.]+)/);
        if (match) {
          return match[1];
        }
      }

      // Handle local paths
      const pathParts = repoUrlOrPath.replace(/\\/g, '/').split('/');
      const lastPart = pathParts[pathParts.length - 1];

      // Remove common extensions and clean up
      return lastPart
        .replace(/\.(git|zip|tar\.gz)$/, '')
        .replace(/[^a-zA-Z0-9-_]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '') || 'unknown-project';
    } catch (error) {
      console.warn('Failed to extract project name from:', repoUrlOrPath, error);
      return 'unknown-project';
    }
  }
}
