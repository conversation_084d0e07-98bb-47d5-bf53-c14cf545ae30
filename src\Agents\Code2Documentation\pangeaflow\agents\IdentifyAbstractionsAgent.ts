// src/Agents/Code2Documentation/pangeaflow/agents/IdentifyAbstractionsAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../../pangeaflow/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../../pocketflow/utils/buildPrompt";
import { IDENTIFY_ABSTRACTIONS_PROMPT } from "../../prompts/identifyAbstractions";
import { emitGraphStatus, emitProgress } from "../../utils/events";
import { Abstraction } from "../../types";
import yaml from 'js-yaml';
import { sleep } from '@/utils/sleep';

export class IdentifyAbstractionsAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('identify-abstractions', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("IdentifyAbstractions", 0, "Starting abstraction identification");
    
    try {
      // Extract parameters from context
      const {
        files, // Array of [path, content] from FetchRepoAgent
        selected_files,
        project_name,
        language,
        use_cache,
        user_id,
        tutorial_id,
        max_abstraction_num
      } = context.sharedState;

      console.log('IdentifyAbstractionsAgent: Executing with context:', context.sharedState, context.nodeOutputs);

      // Try to get files from nodeOutputs if not in sharedState
      let filesFromNodeOutputs = null;
      if (!files && context.nodeOutputs && context.nodeOutputs.has('fetch-repo')) {
        const fetchRepoResult = context.nodeOutputs.get('fetch-repo') as any;
        filesFromNodeOutputs = fetchRepoResult?.files;
        console.log('IdentifyAbstractionsAgent: Found files in nodeOutputs:', filesFromNodeOutputs);
      }

      // Use files from FetchRepoAgent, fallback to selected_files for backward compatibility
      const files_data = (files as [string, string][]) || filesFromNodeOutputs || (selected_files as [string, string][]) || [];

      console.log('IdentifyAbstractionsAgent: Using files_data:', files_data);
      const lang = language as string ?? 'english';
      const useCache = use_cache as boolean ?? true;
      const maxAbstractionNum = max_abstraction_num as number ?? 10;

      // Build context string and file index list
      let contextStr = '';
      const temp_file_listing_for_prompt: [number, string][] = [];

      emitGraphStatus("IdentifyAbstractions", 10, `Processing ${files_data.length} files for abstraction analysis`);

      console.log(`Processing ${files_data.length} files:`);
      files_data.forEach(([path, content], i) => {
        console.log(`File ${i}: ${path}`);
        contextStr += `--- File Index ${i}: ${path} ---\n${content}\n\n`;
        temp_file_listing_for_prompt.push([i, path]);
      });

      emitGraphStatus("IdentifyAbstractions", 20, "Preparing file listing for analysis");

      const file_listing_for_prompt = temp_file_listing_for_prompt
        .map(([idx, p]) => `- ${idx} # ${p}`)
        .join('\n');

      emitGraphStatus("IdentifyAbstractions", 30, "Preparation complete, ready for abstraction analysis");

      // Prepare language instructions
      let language_instruction = '';
      let name_lang_hint = '';
      let desc_lang_hint = '';

      if (lang.toLowerCase() !== 'english') {
        const capLang = lang.charAt(0).toUpperCase() + lang.slice(1);
        language_instruction = `IMPORTANT: Generate the \`name\` and \`description\` in **${capLang}** language. Do NOT use English for these fields.\n\n`;
        name_lang_hint = ` (in ${capLang})`;
        desc_lang_hint = ` (in ${capLang})`;
      }

      emitGraphStatus("IdentifyAbstractions", 40, "Starting LLM analysis to identify core abstractions");
      console.log('Identifying abstractions using LLM...');

      const prompt = buildPrompt(IDENTIFY_ABSTRACTIONS_PROMPT, {
        project_name,
        context: contextStr,
        language_instruction,
        max_abstractions: maxAbstractionNum,
        name_lang_hint,
        desc_lang_hint,
        file_listing_for_prompt
      });
    

      emitGraphStatus("IdentifyAbstractions", 50, "Sending request to LLM for abstraction identification");
      const response = await callLlm_openrouter({
        tutorial_id: tutorial_id as string,
        prompt, 
        use_cache: useCache && (context.sharedState.retryCount || 0) === 0, 
        user_id: user_id as string
      });
      
      emitGraphStatus("IdentifyAbstractions", 60, "Received response from LLM, processing results");

      // Validation - Extract YAML between ```yaml
      const yaml_match = response.match(/```yaml([\s\S]*?)```/);
      const yaml_str = yaml_match ? yaml_match[1] : response;
      
      if (!yaml_str) {
        emitGraphStatus("IdentifyAbstractions", 65, "Error: LLM did not return YAML block");
        throw new Error('LLM did not return YAML block');
      }

      emitGraphStatus("IdentifyAbstractions", 70, "Parsing YAML response");
      console.log('📄 Raw YAML string length:', yaml_str.length);
      const abstractions = yaml.load(yaml_str) as any;

      console.log('📊 Parsed abstractions count:', Array.isArray(abstractions) ? abstractions.length : 'Not an array');
      if (Array.isArray(abstractions) && abstractions.length > 0) {
        console.log('📋 First abstraction structure:', JSON.stringify(abstractions[0], null, 2));
      }
      if (!Array.isArray(abstractions)) {
        emitGraphStatus("IdentifyAbstractions", 75, "Error: LLM output is not a list");
        throw new Error('LLM Output is not a list');
      }

      emitGraphStatus("IdentifyAbstractions", 80, `Found ${abstractions.length} core abstractions`);

      // Validate and transform
      emitGraphStatus("IdentifyAbstractions", 85, "Validating and transforming abstractions");

      const result = abstractions.map((item: any) => {
        if (typeof item.name !== 'string' || typeof item.description !== 'string' || !Array.isArray(item.file_indices)) {
          emitGraphStatus("IdentifyAbstractions", 87, `Error: Invalid abstraction format: ${JSON.stringify(item)}`);
          throw new Error(`Invalid abstraction format: ${JSON.stringify(item)}`);
        }

        // Parse indices
        const validatedIndices: number[] = [];

        for (const entry of item.file_indices) {
          try {
            let idx: number;

            if (typeof entry === 'number') {
              idx = entry;
            } else {
              // Convert to string and apply robust parsing
              const entryStr = String(entry).trim();

              // Handle various patterns:
              // - "8" -> 8
              // - "8 # filename" -> 8
              // - "8 in line Query Processing & Execution" -> 8
              // - "File Index 8: filename" -> 8
              // - "8: filename" -> 8

              if (entryStr.includes('#')) {
                // Pattern: "8 # filename" or "8#filename"
                const beforeHash = entryStr.split('#')[0].trim();
                idx = parseInt(beforeHash, 10);
              } else if (entryStr.includes(':')) {
                // Pattern: "8: filename" or "File Index 8: filename"
                const colonParts = entryStr.split(':');
                const beforeColon = colonParts[0].trim();
                // Extract number from "File Index 8" or just "8"
                const numberMatch = beforeColon.match(/(\d+)$/);
                if (numberMatch) {
                  idx = parseInt(numberMatch[1], 10);
                } else {
                  idx = parseInt(beforeColon, 10);
                }
              } else if (entryStr.includes(' in ')) {
                // Pattern: "8 in line Query Processing & Execution"
                const inParts = entryStr.split(' in ');
                const beforeIn = inParts[0].trim();
                idx = parseInt(beforeIn, 10);
              } else {
                // Extract first number found in the string
                const numberMatch = entryStr.match(/(\d+)/);
                if (numberMatch) {
                  idx = parseInt(numberMatch[1], 10);
                } else {
                  throw new Error(`No number found in entry: ${entryStr}`);
                }
              }
            }

            if (isNaN(idx)) {
              console.error(`❌ Parsed index is NaN for entry: "${entry}" in item: ${item.name}`);
              throw new Error(`Parsed index is NaN for entry: ${entry}`);
            }

            if (!(0 <= idx && idx < files_data.length)) {
              console.error(`❌ Invalid file index ${idx} (valid range: 0-${files_data.length - 1}) for entry: "${entry}" in item: ${item.name}`);
              emitGraphStatus("IdentifyAbstractions", 88, `Error: Invalid file index ${idx} found in item ${item.name}`);
              throw new Error(`Invalid file index ${idx} found in item ${item.name}. Max index is ${files_data.length - 1}.`);
            }

            validatedIndices.push(idx);
          } catch (error) {
            console.error(`❌ Error processing entry "${entry}" in item "${item.name}":`, error);
            emitGraphStatus("IdentifyAbstractions", 89, `Error: Could not parse index from entry: ${entry} in item ${item.name}`);
            throw new Error(`Could not parse index from entry: ${entry} in item ${item.name}. Error: ${(error as Error).message}`);
          }
        }

        // Remove duplicates and sort
        const fileIndices = [...new Set(validatedIndices)].sort((a, b) => a - b);

        return {
          name: item.name,
          description: item.description,
          files: fileIndices
        };
      }) as Abstraction[];

      emitGraphStatus("IdentifyAbstractions", 90, "Successfully validated all abstractions");

      // Emit progress event
      emitProgress("Abstraction Identification", 40, `Identified ${result.length} core abstractions`);

      // Final graph status
      emitGraphStatus("IdentifyAbstractions", 100, "Abstraction identification complete");

      // Return success result with next action
      return {
        success: true,
        output: {
          abstractions: result
        },
        events: [],
        nextActions: ['analyze-relationships'],
        sharedStateUpdates: {
          ...context.sharedState,
          abstractions: result
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = (context.sharedState.retryCount as number) || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['identify-abstractions'],
          sharedStateUpdates: {
            ...context.sharedState,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        sharedStateUpdates: context.sharedState
      };
    }
  }
}
