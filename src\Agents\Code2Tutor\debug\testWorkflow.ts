// Debug script for testing Code2Tutor workflow
// This script helps identify where the workflow might be failing

import { executeCode2TutorFlow, createDefaultSharedStore } from '../index';
import { tutorEvents, TutorEventType } from '../utils/events';

/**
 * Test the Code2Tutor workflow with detailed logging
 */
export async function testCode2TutorWorkflow() {
  console.log('🧪 Starting Code2Tutor workflow test...');

  // Set up event listeners for detailed monitoring
  const progressListener = tutorEvents.on(TutorEventType.PROGRESS, (event) => {
    console.log(`📊 Progress: ${event.stage} - ${event.progress}% - ${event.message || ''}`);
  });

  const agentStatusListener = tutorEvents.on(TutorEventType.AGENT_STATUS, (event) => {
    console.log(`🤖 Agent ${event.agentName}: ${event.status} (${event.progress}%) - ${event.message || ''}`);
  });

  const conceptListener = tutorEvents.on(TutorEventType.CONCEPT_EXTRACTED, (event) => {
    console.log(`💡 Concept extracted: ${event.name} (${event.difficulty})`);
  });

  const sectionListener = tutorEvents.on(TutorEventType.SECTION_GENERATED, (event) => {
    console.log(`📝 Section generated: ${event.title}`);
  });

  const completeListener = tutorEvents.on(TutorEventType.COMPLETE, (event) => {
    if (event.success) {
      console.log(`✅ Tutorial completed successfully!`);
      console.log(`🔗 Tutorial URL: ${event.tutorialUrl}`);
    } else {
      console.error(`❌ Tutorial failed: ${event.message}`);
    }
  });

  const errorListener = tutorEvents.on(TutorEventType.ERROR, (event) => {
    console.error(`💥 Error: ${event.message}`);
  });

  try {
    // Create a test shared store with minimal configuration
    const shared = createDefaultSharedStore({
      user_id: 'test-user-123',
      repo_url: 'https://github.com/microsoft/TypeScript',
      project_name: 'TypeScript Test Project',
      target_audience: 'beginner',
      content_language: 'english',
      tutorial_format: 'guided',
      include_exercises: true,
      include_diagrams: false,
      include_examples: true,
      max_concepts: 3, // Keep it small for testing
      selected_files: [
        'src/compiler/types.ts',
        'src/compiler/checker.ts'
      ],
      language: 'typescript',
      use_cache: false // Disable cache for testing
    });

    console.log('📋 Test configuration:', {
      user_id: shared.user_id,
      repo_url: shared.repo_url,
      project_name: shared.project_name,
      target_audience: shared.target_audience,
      max_concepts: shared.max_concepts,
      selected_files: shared.selected_files
    });

    console.log('🚀 Starting workflow execution...');
    
    // Execute the workflow
    const results = await executeCode2TutorFlow(shared);
    
    console.log('📊 Workflow Results:', {
      totalSteps: results?.length || 0,
      results: results
    });

    // Analyze results
    if (results && results.length > 0) {
      results.forEach((result, index) => {
        console.log(`Step ${index + 1}:`, {
          success: result.success,
          error: result.error?.message,
          data: result.data ? Object.keys(result.data) : 'No data',
          nextActions: result.nextActions
        });
      });
    } else {
      console.warn('⚠️ No results returned from workflow');
    }

    return results;

  } catch (error) {
    console.error('💥 Workflow test failed:', error);
    throw error;
  } finally {
    // Clean up event listeners
    progressListener();
    agentStatusListener();
    conceptListener();
    sectionListener();
    completeListener();
    errorListener();
    
    console.log('🧹 Event listeners cleaned up');
  }
}

/**
 * Test individual agents in isolation
 */
export async function testIndividualAgents() {
  console.log('🔬 Testing individual agents...');
  
  // Test ConceptExtractionAgent
  try {
    const { ConceptExtractionAgent } = await import('../agents/ConceptExtractionAgent');
    const agent = new ConceptExtractionAgent(tutorEvents, console);
    
    console.log('✅ ConceptExtractionAgent imported successfully');
  } catch (error) {
    console.error('❌ ConceptExtractionAgent import failed:', error);
  }

  // Test other agents...
  // Add more individual agent tests as needed
}

// Export for use in browser console or Node.js
if (typeof window !== 'undefined') {
  (window as any).testCode2TutorWorkflow = testCode2TutorWorkflow;
  (window as any).testIndividualAgents = testIndividualAgents;
}
