# API Reference

Complete API documentation for PangeaFlow classes, interfaces, and functions.

## Core Classes

### WorkflowBuilder

The main entry point for creating workflows.

```typescript
class WorkflowBuilder {
  static create(): WorkflowBuilder
  
  addReasoningAgent(id: string, llmProvider: LLMProvider): this
  addToolAgent(id: string, tools: Record<string, Tool>): this
  addMemoryAgent(id: string): this
  route(action: string, ...componentIds: string[]): this
  build(): WorkflowOrchestrator
}
```

#### Methods

##### `static create(): WorkflowBuilder`
Creates a new WorkflowBuilder instance.

**Returns:** New WorkflowBuilder instance

**Example:**
```typescript
const builder = WorkflowBuilder.create();
```

##### `addReasoningAgent(id: string, llmProvider: LLMProvider): this`
Adds a reasoning agent that uses LLMs for decision-making.

**Parameters:**
- `id` - Unique identifier for the agent
- `llmProvider` - Function that calls your LLM service

**Returns:** WorkflowBuilder instance for chaining

**Example:**
```typescript
builder.addReasoningAgent('planner', async (prompt, context) => {
  return await callOpenAI(prompt);
});
```

##### `addToolAgent(id: string, tools: Record<string, Tool>): this`
Adds a tool agent that executes functions and tools.

**Parameters:**
- `id` - Unique identifier for the agent
- `tools` - Object mapping tool names to functions

**Returns:** WorkflowBuilder instance for chaining

**Example:**
```typescript
builder.addToolAgent('file-processor', {
  'read-file': async (path) => await fs.readFile(path),
  'write-file': async ({path, content}) => await fs.writeFile(path, content)
});
```

##### `addMemoryAgent(id: string): this`
Adds a memory agent for state management and data storage.

**Parameters:**
- `id` - Unique identifier for the agent

**Returns:** WorkflowBuilder instance for chaining

**Example:**
```typescript
builder.addMemoryAgent('cache');
```

##### `route(action: string, ...componentIds: string[]): this`
Defines routing between workflow components.

**Parameters:**
- `action` - Action name that triggers this route
- `componentIds` - One or more component IDs to route to

**Returns:** WorkflowBuilder instance for chaining

**Example:**
```typescript
builder
  .route('start', 'planner')
  .route('process', 'worker1', 'worker2')  // Parallel routing
  .route('error', 'error-handler');
```

##### `build(): WorkflowOrchestrator`
Builds and returns the configured workflow orchestrator.

**Returns:** Configured WorkflowOrchestrator instance

### WorkflowOrchestrator

Manages workflow execution and component coordination.

```typescript
class WorkflowOrchestrator {
  registerComponent(component: AgentComponent): this
  defineRoute(action: string, componentIds: ComponentId[]): this
  execute(startAction: string, context: ExecutionContext): Promise<ExecutionResult[]>
  getMetrics(): TelemetryStats
  on(eventType: string, handler: EventHandler): () => void
}
```

#### Methods

##### `execute(startAction: string, context: ExecutionContext): Promise<ExecutionResult[]>`
Executes the workflow starting with the specified action.

**Parameters:**
- `startAction` - Action to start workflow execution
- `context` - Execution context with metadata

**Returns:** Promise resolving to array of execution results

**Example:**
```typescript
const results = await workflow.execute('start', {
  metadata: { 
    input: 'Analyze this repository',
    userId: 'user123'
  }
});
```

##### `on(eventType: string, handler: EventHandler): () => void`
Subscribes to workflow events.

**Parameters:**
- `eventType` - Type of event to listen for
- `handler` - Function to handle the event

**Returns:** Function to unsubscribe from the event

**Example:**
```typescript
const unsubscribe = workflow.on('agent.status', (event) => {
  console.log('Agent status:', event.payload);
});

// Later, unsubscribe
unsubscribe();
```

### AgentComponent

Base class for all workflow components.

```typescript
abstract class AgentComponent {
  readonly id: ComponentId
  protected readonly eventBus: EventBus
  protected readonly telemetry: TelemetryCollector
  protected readonly state: ReactiveState<Record<string, unknown>>
  
  constructor(id: string, eventBus: EventBus, telemetry: TelemetryCollector, initialState?: Record<string, unknown>)
  
  abstract execute(context: ExecutionContext): Promise<ExecutionResult>
  protected emit<T>(type: string, payload: T, correlationId?: string): void
  protected withTelemetry<T>(operation: string, fn: () => Promise<T>): Promise<T>
}
```

#### Methods

##### `abstract execute(context: ExecutionContext): Promise<ExecutionResult>`
Main execution method that must be implemented by subclasses.

**Parameters:**
- `context` - Execution context with metadata and correlation ID

**Returns:** Promise resolving to execution result

##### `protected emit<T>(type: string, payload: T, correlationId?: string): void`
Emits an event to the workflow event bus.

**Parameters:**
- `type` - Event type identifier
- `payload` - Event data
- `correlationId` - Optional correlation ID for tracing

**Example:**
```typescript
this.emit('agent.status', {
  agentName: 'MyAgent',
  status: 'processing',
  progress: 50
});
```

##### `protected withTelemetry<T>(operation: string, fn: () => Promise<T>): Promise<T>`
Wraps an operation with telemetry collection.

**Parameters:**
- `operation` - Name of the operation for telemetry
- `fn` - Function to execute with telemetry

**Returns:** Promise resolving to the function result

**Example:**
```typescript
const result = await this.withTelemetry('data-processing', async () => {
  return await this.processData(data);
});
```

## Built-in Agents

### ReasoningAgent

Agent that uses LLMs for reasoning and decision-making.

```typescript
class ReasoningAgent extends AgentComponent {
  constructor(id: string, eventBus: EventBus, telemetry: TelemetryCollector, llmProvider: LLMProvider)
}
```

### ToolAgent

Agent that executes tools and functions.

```typescript
class ToolAgent extends AgentComponent {
  constructor(id: string, eventBus: EventBus, telemetry: TelemetryCollector, tools: Map<string, Tool>)
}
```

### MemoryAgent

Agent for state management and data storage.

```typescript
class MemoryAgent extends AgentComponent {
  constructor(id: string, eventBus: EventBus, telemetry: TelemetryCollector)
}
```

## Interfaces and Types

### ExecutionContext

Context passed to agent execution methods.

```typescript
interface ExecutionContext {
  metadata: Record<string, unknown>  // Request metadata
  correlationId?: string             // For request tracing
}
```

### ExecutionResult

Result returned by agent execution.

```typescript
interface ExecutionResult {
  success: boolean                   // Execution status
  data?: unknown                     // Result data
  error?: Error                      // Error information
  events: WorkflowEvent[]           // Events to emit
  nextActions: string[]             // Next actions to execute
  metadata: Record<string, unknown> // Additional metadata
}
```

### WorkflowEvent

Structure of all workflow events.

```typescript
interface WorkflowEvent<T = unknown> {
  id: string                    // Unique event ID
  type: string                  // Event type
  timestamp: number             // Event timestamp
  source: ComponentId           // Source component
  payload: T                    // Event data
  correlationId?: string        // Correlation ID
}
```

### LLMProvider

Function signature for LLM providers.

```typescript
type LLMProvider = (
  prompt: string,
  context: Record<string, unknown>
) => Promise<string>
```

### Tool

Function signature for tools.

```typescript
type Tool = (args: unknown) => Promise<unknown>
```

### TelemetryData

Structure for telemetry data.

```typescript
interface TelemetryData {
  componentId: ComponentId
  operation: string
  duration: number
  success: boolean
  metadata: Record<string, unknown>
}
```

## Utility Classes

### EventBus

Manages event publishing and subscription.

```typescript
class EventBus {
  emit<T>(type: string, payload: T, source: ComponentId, correlationId?: string): void
  on(type: string, listener: (event: WorkflowEvent) => void): () => void
}
```

### ReactiveState

Manages reactive state with automatic invalidation.

```typescript
class ReactiveState<T> {
  get(): T
  set(newState: T): void
  subscribe(callback: (state: T) => void): () => void
}
```

### TelemetryCollector

Collects and manages telemetry data.

```typescript
class TelemetryCollector {
  record(data: TelemetryData): void
  getStats(): TelemetryStats
}
```

### StreamingWorkflow

Handles streaming and batch processing.

```typescript
class StreamingWorkflow {
  constructor(orchestrator: WorkflowOrchestrator)
  
  processStream<T>(
    stream: AsyncIterable<T>,
    startAction: string,
    batchSize?: number
  ): AsyncGenerator<ExecutionResult[], void, unknown>
}
```

## Error Types

### WorkflowError

Base error class for workflow-related errors.

```typescript
class WorkflowError extends Error {
  constructor(message: string, public readonly componentId?: ComponentId)
}
```

### AgentExecutionError

Error thrown during agent execution.

```typescript
class AgentExecutionError extends WorkflowError {
  constructor(
    message: string,
    componentId: ComponentId,
    public readonly originalError?: Error
  )
}
```

## Constants

### Standard Event Types

```typescript
const EventTypes = {
  AGENT_STATUS: 'agent.status',
  AGENT_STARTED: 'agent.started',
  AGENT_COMPLETED: 'agent.completed',
  AGENT_FAILED: 'agent.failed',
  AGENT_ERROR: 'agent.error',
  
  STEP_STARTED: 'step.started',
  STEP_COMPLETED: 'step.completed',
  STEP_FAILED: 'step.failed',
  
  WORKFLOW_STARTED: 'workflow.started',
  WORKFLOW_COMPLETED: 'workflow.completed',
  WORKFLOW_FAILED: 'workflow.failed',
  
  DATA_RECEIVED: 'data.received',
  DATA_PROCESSED: 'data.processed',
  DATA_VALIDATED: 'data.validated',
  DATA_STORED: 'data.stored',
  
  STATE_UPDATED: 'state.updated',
  STATE_SYNCED: 'state.synced'
} as const;
```

## Usage Examples

### Basic Workflow Creation

```typescript
import { WorkflowBuilder } from '@/pangeaflow';

const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .addMemoryAgent('storage')
  .route('start', 'planner')
  .route('execute', 'executor')
  .route('store', 'storage')
  .build();
```

### Custom Agent Implementation

```typescript
class CustomAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('custom-agent', eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.withTelemetry('custom-operation', async () => {
        return await this.performCustomWork(context.metadata);
      });
      
      this.emit('custom.completed', { result });
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: {}
      };
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: {}
      };
    }
  }
  
  private async performCustomWork(metadata: Record<string, unknown>) {
    // Custom implementation
    return { processed: true };
  }
}
```

### Event Handling

```typescript
// Listen to workflow events
workflow.on('agent.status', (event) => {
  console.log(`${event.source}: ${event.payload.message}`);
});

workflow.on('workflow.completed', (event) => {
  console.log('Workflow finished:', event.payload);
});

// Execute workflow
const results = await workflow.execute('start', {
  metadata: { input: 'process this data' }
});
```

## Next Steps

- [Getting Started](./getting-started.md) - Build your first workflow
- [Examples](./examples.md) - See complete implementation examples
- [Troubleshooting](./troubleshooting.md) - Common issues and solutions
