/**
 * SimplePangeaFlow - A simplified interface for PangeaFlow
 * 
 * Provides an easier entry point for junior developers while maintaining
 * access to all PangeaFlow capabilities under the hood.
 */
import {
  WorkflowBuilder,
  WorkflowOrchestrator,
  EventBus,
  TelemetryCollector
} from './pangeaflow';

/**
 * Creates a simple workflow with minimal configuration
 */
export function createSimpleWorkflow(config: {
  agents: Array<{
    id: string,
    type: 'reasoning' | 'tool' | 'memory',
    handler?: any
  }>,
  routes: Array<{
    from: string,
    to: string | string[]
  }>
}): WorkflowOrchestrator {
  const builder = WorkflowBuilder.create();
  const eventBus = new EventBus();
  const telemetry = new TelemetryCollector();
  
  // Add agents based on type
  for (const agent of config.agents) {
    if (agent.type === 'reasoning') {
      builder.addReasoningAgent(agent.id, agent.handler);
    } else if (agent.type === 'tool') {
      builder.addToolAgent(agent.id, agent.handler);
    } else if (agent.type === 'memory') {
      builder.addMemoryAgent(agent.id);
    }
  }
  
  // Add routes
  for (const route of config.routes) {
    const destinations = Array.isArray(route.to) ? route.to : [route.to];
    builder.route(route.from, ...destinations);
  }
  
  return builder.build();
}

/**
 * Run a simple workflow with minimal configuration
 */
export async function runSimpleWorkflow(
  workflow: WorkflowOrchestrator,
  startAction: string,
  initialData: Record<string, any> = {}
): Promise<any> {
  try {
    const results = await workflow.execute(startAction, {
      sharedState: initialData
    });
    
    return {
      success: true,
      results
    };
  } catch (error) {
    return {
      success: false,
      error
    };
  }
}