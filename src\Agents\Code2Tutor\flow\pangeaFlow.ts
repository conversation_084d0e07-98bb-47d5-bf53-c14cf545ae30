// src/Agents/Code2Tutor/flow/pangeaFlow.ts

import {
  WorkflowBuilder,
  WorkflowOrchestrator,
  EventBus,
  TelemetryCollector
} from '@/pangeaflow';
import { SharedStore } from '../types';
import { 
  RepoAnalysisAgent,
  ConceptExtractionAgent,
  TutorialPlanningAgent,
  ContentGenerationAgent,
  TutorialAssemblyAgent
} from '../agents';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';

/**
 * Creates and configures the Code2Tutor PangeaFlow workflow
 * 
 * This function sets up the complete workflow for transforming code into educational tutorials:
 * 1. Repository Analysis - Fetch and analyze code files
 * 2. Concept Extraction - Identify key learning concepts
 * 3. Tutorial Planning - Structure the learning progression
 * 4. Content Generation - Create educational content
 * 5. Tutorial Assembly - Combine into final tutorial
 */
export function createCode2TutorFlow(): WorkflowOrchestrator {
  const builder = WorkflowBuilder.create();

  // Configure LLM provider for reasoning agents
  const llmProvider = async (prompt: string, context: Record<string, unknown>) => {
    const shared = context.shared as SharedStore;
    
    return await callLlm_openrouter({
      tutorial_id: shared?.tutorial_id,
      prompt,
      temperature: 0.7,
      model: "google/gemini-2.5-flash-preview-05-20",
      use_cache: shared?.use_cache || false,
      user_id: shared?.user_id || 'anonymous'
    });
  };

  // Create event bus and telemetry instances
  const eventBus = new EventBus();
  const telemetry = new TelemetryCollector();

  // Create agent instances
  const repoAnalysisAgent = new RepoAnalysisAgent(eventBus, telemetry);
  const conceptExtractionAgent = new ConceptExtractionAgent(eventBus, telemetry);
  const tutorialPlanningAgent = new TutorialPlanningAgent(eventBus, telemetry);
  const contentGenerationAgent = new ContentGenerationAgent(eventBus, telemetry);
  const tutorialAssemblyAgent = new TutorialAssemblyAgent(eventBus, telemetry);

  // Register agents directly with the orchestrator
  const orchestrator = builder['orchestrator'] as any;
  orchestrator.registerComponent(repoAnalysisAgent);
  orchestrator.registerComponent(conceptExtractionAgent);
  orchestrator.registerComponent(tutorialPlanningAgent);
  orchestrator.registerComponent(contentGenerationAgent);
  orchestrator.registerComponent(tutorialAssemblyAgent);

  // Add reasoning agent for coordination and error handling
  builder.addReasoningAgent('coordinator', llmProvider);

  // Add memory agent for state management
  builder.addMemoryAgent('memory');

  // Define workflow routes
  builder
    .route('start', 'repo-analysis')
    .route('extract-concepts', 'concept-extraction')
    .route('plan-tutorial', 'tutorial-planning')
    .route('generate-content', 'content-generation')
    .route('assemble-tutorial', 'tutorial-assembly')
    .route('complete', 'memory')
    .route('error', 'coordinator');

  return builder.build();
}

/**
 * Execute the Code2Tutor workflow with the provided shared store
 */
export async function executeCode2TutorFlow(shared: SharedStore): Promise<any> {
  const workflow = createCode2TutorFlow();

  try {
    // Validate critical fields before starting workflow
    if (!shared.tutorial_id) {
      throw new Error('Tutorial ID is missing from shared store');
    }

    if (!shared.user_id) {
      throw new Error('User ID is missing from shared store');
    }

    console.log('🚀 Starting Code2Tutor workflow execution with config:', {
      tutorial_id: shared.tutorial_id,
      user_id: shared.user_id,
      repo_url: shared.repo_url,
      project_name: shared.project_name,
      target_audience: shared.target_audience,
      selected_files: shared.selected_files?.length || 0
    });

    // Set up comprehensive event listeners for monitoring
    const progressListener = workflow.on('agent.status', (event) => {
      const payload = event.payload as any;
      console.log(`🤖 Agent ${payload?.agentName || 'Unknown'}: ${payload?.message || 'Status update'}`);
    });

    const errorListener = workflow.on('error', (event) => {
      const payload = event.payload as any;
      console.error('💥 Workflow error detected:', payload);
      // Emit error event for UI to catch
      import('../utils/events').then(({ emitTutorError }) => {
        emitTutorError(payload?.message || 'Unknown workflow error', payload);
      }).catch(err => {
        console.error('Failed to emit tutor error:', err);
      });
    });

    const stepListener = workflow.on('step.completed', (event) => {
      console.log(`✅ Step completed:`, event.payload);
    });

    const stepErrorListener = workflow.on('step.failed', (event) => {
      console.error(`❌ Step failed:`, event.payload);
    });

    // Execute the workflow
    console.log('🔄 Executing workflow...');
    const results = await workflow.execute('start', {
      sharedState: { shared },
      nodeOutputs: new Map([['shared', shared]])
    });

    console.log('📊 Workflow execution completed. Raw results:', results);

    // Analyze results for errors
    if (!results) {
      const error = new Error('Workflow returned no results');
      console.error('💥 Workflow failure: No results returned for tutorial_id:', shared.tutorial_id);
      throw error;
    }

    console.log('📊 Analyzing workflow results for tutorial_id:', shared.tutorial_id);

    if (Array.isArray(results)) {
      const failedSteps = results.filter(result => !result.success);
      if (failedSteps.length > 0) {
        console.error('💥 Workflow had failed steps for tutorial_id:', shared.tutorial_id, failedSteps);
        const errorMessage = `Workflow failed at ${failedSteps.length} step(s): ${failedSteps.map(s => s.error?.message || 'Unknown error').join(', ')}`;
        const detailedError = new Error(`Tutorial ${shared.tutorial_id}: ${errorMessage}`);

        console.error('💥 Detailed failure information:', {
          tutorial_id: shared.tutorial_id,
          user_id: shared.user_id,
          failed_steps: failedSteps.length,
          error_details: failedSteps,
          timestamp: new Date().toISOString()
        });

        throw detailedError;
      }

      console.log('✅ All workflow steps completed successfully for tutorial_id:', shared.tutorial_id);
    }

    // Clean up listeners
    progressListener();
    errorListener();
    stepListener();
    stepErrorListener();

    return results;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('💥 Code2Tutor workflow execution failed:', {
      tutorial_id: shared.tutorial_id,
      user_id: shared.user_id,
      error: errorMessage,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
}

/**
 * Utility function to create a shared store with default values
 */
export function createDefaultSharedStore(overrides: Partial<SharedStore> = {}): SharedStore {
  // Generate a unique tutorial_id if not provided
  const defaultTutorialId = `tutorial-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  return {
    user_id: 'anonymous',
    tutorial_id: defaultTutorialId, // Ensure tutorial_id is always present
    target_audience: 'beginner',
    content_language: 'english',
    tutorial_format: 'guided',
    include_exercises: true,
    include_diagrams: true,
    include_examples: true,
    max_concepts: 8,
    selected_files: [],
    language: 'javascript',
    use_cache: true,
    final_output_dir: 'output',
    ...overrides
  };
}

/**
 * Validate shared store before workflow execution
 */
export function validateSharedStore(shared: SharedStore): string[] {
  const errors: string[] = [];

  if (!shared.tutorial_id) {
    errors.push('tutorial_id is required');
  }

  if (!shared.user_id) {
    errors.push('user_id is required');
  }

  if (!shared.repo_url && !shared.local_dir) {
    errors.push('Either repo_url or local_dir must be provided');
  }

  if (!shared.project_name) {
    errors.push('project_name is required');
  }

  if (!['beginner', 'intermediate', 'advanced'].includes(shared.target_audience)) {
    errors.push('target_audience must be beginner, intermediate, or advanced');
  }

  if (!['interactive', 'guided', 'self-paced'].includes(shared.tutorial_format)) {
    errors.push('tutorial_format must be interactive, guided, or self-paced');
  }

  if (shared.max_concepts < 3 || shared.max_concepts > 15) {
    errors.push('max_concepts must be between 3 and 15');
  }

  return errors;
}
