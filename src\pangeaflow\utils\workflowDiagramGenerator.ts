/**
 * PangeaFlow Workflow Diagram Generator
 * 
 * Generates PNG diagrams showing the actual workflow flow between actions,
 * similar to traditional flowcharts where each step leads to the next.
 */

import { WorkflowOrchestrator, ComponentId, AgentComponent } from '../pangeaflow';
import { createCanvas } from 'canvas';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Options for customizing the generated workflow diagram
 */
export interface PangeaFlowDiagramOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  nodeColor?: string;
  edgeColor?: string;
  textColor?: string;
  fontSize?: number;
  padding?: number;
  showComponentDetails?: boolean;
}

/**
 * Represents a workflow step (action) in the diagram
 */
interface WorkflowNode {
  id: string;
  name: string;
  position: { x: number; y: number };
  componentNames: string[];
}

/**
 * Represents a connection between workflow steps
 */
interface WorkflowEdge {
  from: string;
  to: string;
  label?: string;
}

/**
 * Main class for generating workflow diagrams from PangeaFlow
 */
export class PangeaFlowDiagramGenerator {
  private readonly orchestrator: WorkflowOrchestrator;
  private readonly options: Required<PangeaFlowDiagramOptions>;

  constructor(orchestrator: WorkflowOrchestrator, options?: PangeaFlowDiagramOptions) {
    this.orchestrator = orchestrator;
    this.options = {
      width: options?.width || 1200,
      height: options?.height || 800,
      backgroundColor: options?.backgroundColor || '#ffffff',
      nodeColor: options?.nodeColor || '#e3f2fd',
      edgeColor: options?.edgeColor || '#1976d2',
      textColor: options?.textColor || '#212121',
      fontSize: options?.fontSize || 12,
      padding: options?.padding || 50,
      showComponentDetails: options?.showComponentDetails ?? true,
    };
  }

  /**
   * Generate and save a PNG diagram of the workflow
   */
  async generatePNG(outputPath: string): Promise<void> {
    // Ensure output directory exists
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // Analyze workflow structure
    const { nodes, edges } = this.analyzeWorkflow();

    // Create canvas
    const canvas = createCanvas(this.options.width, this.options.height);
    const ctx = canvas.getContext('2d');

    // Draw background
    ctx.fillStyle = this.options.backgroundColor;
    ctx.fillRect(0, 0, this.options.width, this.options.height);

    // Calculate positions
    this.calculateNodePositions(nodes);

    // Draw workflow
    this.drawEdges(ctx, edges, nodes);
    this.drawNodes(ctx, nodes);

    // Save to file
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(outputPath, buffer);

    console.log(`PangeaFlow diagram generated: ${outputPath}`);
  }

  /**
   * Analyze the workflow to extract nodes and flow connections
   */
  private analyzeWorkflow(): { nodes: Map<string, WorkflowNode>; edges: WorkflowEdge[] } {
    const nodes = new Map<string, WorkflowNode>();
    const edges: WorkflowEdge[] = [];

    // Extract routes and components
    const routesMap = (this.orchestrator as any).routes as Map<string, ComponentId[]>;
    const componentsMap = (this.orchestrator as any).components as Map<ComponentId, AgentComponent>;

    // Create nodes for each action
    routesMap.forEach((componentIds, actionName) => {
      const componentNames = componentIds.map(id => {
        const component = componentsMap.get(id);
        if (!component) return id;
        
        const componentType = component.constructor.name;
        const formattedName = this.formatComponentName(id);
        
        return this.options.showComponentDetails 
          ? `${formattedName}\n(${componentType})`
          : formattedName;
      });

      nodes.set(actionName, {
        id: actionName,
        name: this.formatActionName(actionName),
        position: { x: 0, y: 0 },
        componentNames,
      });
    });

    // Determine workflow flow based on common patterns
    edges.push(...this.determineWorkflowFlow(Array.from(nodes.keys())));

    return { nodes, edges };
  }

  /**
   * Determine the flow between workflow actions by analyzing component behavior
   */
  private determineWorkflowFlow(actionNames: string[]): WorkflowEdge[] {
    const edges: WorkflowEdge[] = [];

    // First, try to determine flow from known patterns
    const knownFlows = this.getKnownWorkflowPatterns(actionNames);
    if (knownFlows.length > 0) {
      return knownFlows;
    }

    // If no known patterns, try to infer from action names
    const inferredFlows = this.inferFlowFromActionNames(actionNames);
    if (inferredFlows.length > 0) {
      return inferredFlows;
    }

    // Fallback: create a simple sequential flow
    if (actionNames.length > 1) {
      for (let i = 0; i < actionNames.length - 1; i++) {
        edges.push({
          from: actionNames[i],
          to: actionNames[i + 1],
        });
      }
    }

    return edges;
  }

  /**
   * Get known workflow patterns for common PangeaFlow workflows
   */
  private getKnownWorkflowPatterns(actionNames: string[]): WorkflowEdge[] {
    const edges: WorkflowEdge[] = [];

    // Code2Tutor workflow pattern
    const code2TutorPattern = [
      { from: 'start', to: 'extract-concepts' },
      { from: 'extract-concepts', to: 'plan-tutorial' },
      { from: 'plan-tutorial', to: 'generate-content' },
      { from: 'generate-content', to: 'assemble-tutorial' },
      { from: 'assemble-tutorial', to: 'complete' },
    ];

    // Check if this looks like a Code2Tutor workflow
    const hasCode2TutorActions = ['start', 'extract-concepts', 'plan-tutorial', 'generate-content', 'assemble-tutorial']
      .filter(action => actionNames.includes(action)).length >= 3;

    if (hasCode2TutorActions) {
      code2TutorPattern.forEach(pattern => {
        if (actionNames.includes(pattern.from) && actionNames.includes(pattern.to)) {
          edges.push(pattern);
        }
      });

      // Add error handling
      if (actionNames.includes('error') && actionNames.includes('complete')) {
        edges.push({ from: 'error', to: 'complete' });
      }
    }

    return edges;
  }

  /**
   * Infer workflow flow from action names using common patterns
   */
  private inferFlowFromActionNames(actionNames: string[]): WorkflowEdge[] {
    const edges: WorkflowEdge[] = [];

    // Sort actions by likely execution order
    const sortedActions = this.sortActionsByLikelyOrder(actionNames);

    // Create sequential flow
    for (let i = 0; i < sortedActions.length - 1; i++) {
      edges.push({
        from: sortedActions[i],
        to: sortedActions[i + 1],
      });
    }

    return edges;
  }

  /**
   * Sort actions by their likely execution order based on naming patterns
   */
  private sortActionsByLikelyOrder(actionNames: string[]): string[] {
    const orderPriority: Record<string, number> = {
      'start': 0,
      'init': 1,
      'fetch': 10,
      'analyze': 20,
      'extract': 30,
      'plan': 40,
      'generate': 50,
      'create': 50,
      'process': 60,
      'assemble': 70,
      'combine': 70,
      'review': 80,
      'validate': 80,
      'finalize': 90,
      'complete': 100,
      'finish': 100,
      'error': 1000,
    };

    return actionNames.sort((a, b) => {
      const priorityA = this.getActionPriority(a, orderPriority);
      const priorityB = this.getActionPriority(b, orderPriority);
      return priorityA - priorityB;
    });
  }

  /**
   * Get priority for an action based on keywords in its name
   */
  private getActionPriority(actionName: string, orderPriority: Record<string, number>): number {
    const lowerName = actionName.toLowerCase();

    for (const [keyword, priority] of Object.entries(orderPriority)) {
      if (lowerName.includes(keyword)) {
        return priority;
      }
    }

    // Default priority for unknown actions
    return 50;
  }

  /**
   * Calculate positions for workflow nodes in a top-to-bottom flow
   */
  private calculateNodePositions(nodes: Map<string, WorkflowNode>): void {
    const nodeArray = Array.from(nodes.values());
    const nodeCount = nodeArray.length;
    
    if (nodeCount === 0) return;

    const usableWidth = this.options.width - 2 * this.options.padding;
    const usableHeight = this.options.height - 2 * this.options.padding;
    
    // Arrange nodes vertically for workflow flow
    const verticalSpacing = Math.min(usableHeight / Math.max(nodeCount, 1), 120);
    const centerX = this.options.width / 2;

    nodeArray.forEach((node, index) => {
      node.position = {
        x: centerX,
        y: this.options.padding + (index + 0.5) * verticalSpacing,
      };
    });
  }

  /**
   * Draw workflow nodes on the canvas
   */
  private drawNodes(ctx: any, nodes: Map<string, WorkflowNode>): void {
    ctx.font = `${this.options.fontSize}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    nodes.forEach(node => {
      const { x, y } = node.position;
      const nodeWidth = 200;
      const nodeHeight = this.options.showComponentDetails ? 80 : 50;

      // Draw node box
      ctx.fillStyle = this.options.nodeColor;
      ctx.strokeStyle = this.options.edgeColor;
      ctx.lineWidth = 2;
      
      ctx.beginPath();
      ctx.roundRect(x - nodeWidth/2, y - nodeHeight/2, nodeWidth, nodeHeight, 10);
      ctx.fill();
      ctx.stroke();

      // Draw action name
      ctx.fillStyle = this.options.textColor;
      ctx.font = `bold ${this.options.fontSize + 2}px Arial`;
      ctx.fillText(node.name, x, y - (this.options.showComponentDetails ? 15 : 0));

      // Draw component details if enabled
      if (this.options.showComponentDetails && node.componentNames.length > 0) {
        ctx.font = `${this.options.fontSize - 2}px Arial`;
        const componentText = node.componentNames.join(', ');
        const maxWidth = nodeWidth - 20;
        
        // Simple text wrapping
        if (ctx.measureText(componentText).width > maxWidth) {
          const truncated = componentText.substring(0, 25) + '...';
          ctx.fillText(truncated, x, y + 15);
        } else {
          ctx.fillText(componentText, x, y + 15);
        }
      }
    });
  }

  /**
   * Draw edges between workflow nodes
   */
  private drawEdges(ctx: any, edges: WorkflowEdge[], nodes: Map<string, WorkflowNode>): void {
    ctx.strokeStyle = this.options.edgeColor;
    ctx.fillStyle = this.options.edgeColor;
    ctx.lineWidth = 2;

    edges.forEach(edge => {
      const fromNode = nodes.get(edge.from);
      const toNode = nodes.get(edge.to);
      
      if (!fromNode || !toNode) return;

      const fromY = fromNode.position.y + 40; // Bottom of from node
      const toY = toNode.position.y - 40; // Top of to node
      const x = fromNode.position.x; // Same x for vertical flow

      // Draw line
      ctx.beginPath();
      ctx.moveTo(x, fromY);
      ctx.lineTo(x, toY);
      ctx.stroke();

      // Draw arrow
      const arrowSize = 8;
      ctx.beginPath();
      ctx.moveTo(x - arrowSize, toY - arrowSize);
      ctx.lineTo(x, toY);
      ctx.lineTo(x + arrowSize, toY - arrowSize);
      ctx.closePath();
      ctx.fill();
    });
  }

  /**
   * Format action name for display
   */
  private formatActionName(actionName: string): string {
    return actionName
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Format component name for display
   */
  private formatComponentName(componentId: ComponentId): string {
    return componentId
      .replace(/-/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }
}

/**
 * Utility function to generate a PNG diagram from a PangeaFlow workflow
 */
export async function generatePangeaFlowDiagram(
  orchestrator: WorkflowOrchestrator,
  outputPath: string,
  options?: PangeaFlowDiagramOptions
): Promise<void> {
  const generator = new PangeaFlowDiagramGenerator(orchestrator, options);
  await generator.generatePNG(outputPath);
}
