# Built-in Agents

PangeaFlow provides three powerful built-in agent types that cover most workflow needs. Each agent type is optimized for specific tasks and follows consistent patterns for easy integration.

## ReasoningAgent

The ReasoningAgent uses Large Language Models (LLMs) for reasoning, planning, and content generation tasks.

### Basic Usage

```typescript
const llmProvider = async (prompt: string, context: Record<string, unknown>) => {
  // Your LLM integration (OpenAI, Anthropic, etc.)
  const response = await callYourLLM(prompt);
  return response;
};

builder.addReasoningAgent('planner', llmProvider);
```

### LLM Provider Interface

The LLM provider function receives:
- **prompt**: The text prompt to send to the LLM
- **context**: Additional context data from the workflow

```typescript
type LLMProvider = (
  prompt: string, 
  context: Record<string, unknown>
) => Promise<string>;
```

### Example LLM Providers

#### OpenAI Integration

```typescript
import OpenAI from 'openai';

const openaiProvider = async (prompt: string, context: Record<string, unknown>) => {
  const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  
  const response = await openai.chat.completions.create({
    model: 'gpt-4',
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: prompt }
    ],
    temperature: 0.7,
  });
  
  return response.choices[0].message.content || '';
};
```

#### Anthropic Integration

```typescript
import Anthropic from '@anthropic-ai/sdk';

const anthropicProvider = async (prompt: string, context: Record<string, unknown>) => {
  const anthropic = new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY });
  
  const response = await anthropic.messages.create({
    model: 'claude-3-sonnet-20240229',
    max_tokens: 1000,
    messages: [{ role: 'user', content: prompt }],
  });
  
  return response.content[0].text;
};
```

### Use Cases

- **Planning**: Analyze requirements and create execution plans
- **Content Generation**: Create documentation, tutorials, summaries
- **Decision Making**: Choose between different workflow paths
- **Analysis**: Analyze code, data, or text content
- **Coordination**: Orchestrate complex multi-step processes

### Best Practices

1. **Clear Prompts**: Write specific, clear prompts for better results
2. **Context Usage**: Leverage the context parameter for relevant information
3. **Error Handling**: Handle LLM failures gracefully
4. **Rate Limiting**: Implement rate limiting for API calls

```typescript
const robustLLMProvider = async (prompt: string, context: Record<string, unknown>) => {
  try {
    // Add context to prompt
    const enhancedPrompt = `
Context: ${JSON.stringify(context, null, 2)}

Task: ${prompt}

Please provide a clear, actionable response.
    `;
    
    const response = await callLLMWithRetry(enhancedPrompt);
    return response;
  } catch (error) {
    console.error('LLM call failed:', error);
    return 'Error: Unable to process request. Please try again.';
  }
};
```

## ToolAgent

The ToolAgent executes functions and tools, handling external operations and data processing.

### Basic Usage

```typescript
const tools = {
  'file-reader': async (filePath: string) => {
    return await fs.readFile(filePath, 'utf-8');
  },
  'api-caller': async (url: string) => {
    const response = await fetch(url);
    return await response.json();
  },
  'data-processor': async (data: any[]) => {
    return data.map(item => processItem(item));
  }
};

builder.addToolAgent('file-processor', tools);
```

### Tool Interface

Each tool is a function that:
- Takes any input parameters
- Returns a Promise with the result
- Handles its own errors

```typescript
type Tool = (args: unknown) => Promise<unknown>;
```

### Example Tools

#### File Operations

```typescript
const fileTools = {
  'read-file': async (filePath: string) => {
    try {
      return await fs.readFile(filePath, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to read file ${filePath}: ${error.message}`);
    }
  },
  
  'write-file': async ({ path, content }: { path: string; content: string }) => {
    try {
      await fs.writeFile(path, content, 'utf-8');
      return { success: true, path };
    } catch (error) {
      throw new Error(`Failed to write file ${path}: ${error.message}`);
    }
  },
  
  'list-files': async (directory: string) => {
    try {
      const files = await fs.readdir(directory);
      return files.filter(file => !file.startsWith('.'));
    } catch (error) {
      throw new Error(`Failed to list files in ${directory}: ${error.message}`);
    }
  }
};
```

#### API Integration

```typescript
const apiTools = {
  'github-fetch': async ({ owner, repo, path }: { owner: string; repo: string; path: string }) => {
    const url = `https://api.github.com/repos/${owner}/${repo}/contents/${path}`;
    const response = await fetch(url, {
      headers: { 'Authorization': `token ${process.env.GITHUB_TOKEN}` }
    });
    
    if (!response.ok) {
      throw new Error(`GitHub API error: ${response.statusText}`);
    }
    
    return await response.json();
  },
  
  'slack-notify': async ({ channel, message }: { channel: string; message: string }) => {
    const response = await fetch('https://slack.com/api/chat.postMessage', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.SLACK_TOKEN}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ channel, text: message })
    });
    
    return await response.json();
  }
};
```

#### Data Processing

```typescript
const dataTools = {
  'csv-parser': async (csvContent: string) => {
    const lines = csvContent.split('\n');
    const headers = lines[0].split(',');
    
    return lines.slice(1).map(line => {
      const values = line.split(',');
      return headers.reduce((obj, header, index) => {
        obj[header.trim()] = values[index]?.trim() || '';
        return obj;
      }, {} as Record<string, string>);
    });
  },
  
  'json-validator': async (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString);
      return { valid: true, data: parsed };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  },
  
  'data-aggregator': async (items: any[]) => {
    return {
      count: items.length,
      types: [...new Set(items.map(item => typeof item))],
      sample: items.slice(0, 3)
    };
  }
};
```

### Use Cases

- **File Operations**: Reading, writing, processing files
- **API Integration**: Calling external APIs and services
- **Data Processing**: Transforming, validating, aggregating data
- **System Integration**: Interacting with databases, message queues
- **Utility Functions**: Calculations, formatting, validation

### Best Practices

1. **Error Handling**: Always handle errors in tools
2. **Type Safety**: Use TypeScript for better tool interfaces
3. **Validation**: Validate input parameters
4. **Documentation**: Document tool parameters and return values

```typescript
interface FileReadParams {
  filePath: string;
  encoding?: string;
}

const robustFileTools = {
  'read-file': async (params: FileReadParams) => {
    // Validate input
    if (!params.filePath) {
      throw new Error('filePath is required');
    }
    
    try {
      const content = await fs.readFile(params.filePath, params.encoding || 'utf-8');
      return {
        success: true,
        content,
        size: content.length,
        path: params.filePath
      };
    } catch (error) {
      throw new Error(`Failed to read file ${params.filePath}: ${error.message}`);
    }
  }
};
```

## MemoryAgent

The MemoryAgent manages state, caches data, and stores workflow results.

### Basic Usage

```typescript
builder.addMemoryAgent('cache');
builder.addMemoryAgent('results-store');
builder.addMemoryAgent('workflow-state');
```

### Capabilities

MemoryAgents automatically provide:
- **State Storage**: Store and retrieve workflow state
- **Data Caching**: Cache expensive computation results
- **Result Aggregation**: Collect results from multiple components
- **Session Management**: Maintain state across workflow executions

### Usage Patterns

#### Caching Results

```typescript
// Store expensive computation results
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('analyzer', llmProvider)
  .addMemoryAgent('cache')
  
  .route('start', 'analyzer')
  .route('cache-result', 'cache')  // Store analysis results
  .route('retrieve-cached', 'cache')  // Retrieve if available
  .build();
```

#### State Management

```typescript
// Manage workflow state across steps
const statefulWorkflow = WorkflowBuilder.create()
  .addReasoningAgent('step1', llmProvider)
  .addReasoningAgent('step2', llmProvider)
  .addMemoryAgent('state-manager')
  
  .route('start', 'step1')
  .route('save-state', 'state-manager')
  .route('continue', 'step2')
  .route('final-save', 'state-manager')
  .build();
```

#### Result Aggregation

```typescript
// Collect results from parallel processing
const aggregationWorkflow = WorkflowBuilder.create()
  .addToolAgent('worker1', tools)
  .addToolAgent('worker2', tools)
  .addToolAgent('worker3', tools)
  .addMemoryAgent('aggregator')
  
  .route('start', 'worker1', 'worker2', 'worker3')  // Parallel execution
  .route('collect', 'aggregator')  // Aggregate all results
  .build();
```

### Use Cases

- **Caching**: Store expensive computation results
- **State Management**: Maintain workflow state
- **Result Storage**: Store final workflow outputs
- **Session Data**: Manage user session information
- **Intermediate Results**: Store data between workflow steps

## Agent Interaction Patterns

### Sequential Processing

```typescript
const sequential = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .addMemoryAgent('storage')
  
  .route('start', 'planner')      // Plan the work
  .route('execute', 'executor')   // Execute the plan
  .route('store', 'storage')      // Store results
  .build();
```

### Parallel Processing with Aggregation

```typescript
const parallel = WorkflowBuilder.create()
  .addReasoningAgent('coordinator', llmProvider)
  .addToolAgent('worker1', tools)
  .addToolAgent('worker2', tools)
  .addMemoryAgent('aggregator')
  
  .route('start', 'coordinator')
  .route('distribute', 'worker1', 'worker2')  // Parallel execution
  .route('aggregate', 'aggregator')           // Combine results
  .build();
```

### Feedback Loops

```typescript
const feedback = WorkflowBuilder.create()
  .addReasoningAgent('analyzer', llmProvider)
  .addToolAgent('processor', tools)
  .addReasoningAgent('validator', llmProvider)
  .addMemoryAgent('storage')
  
  .route('start', 'analyzer')
  .route('process', 'processor')
  .route('validate', 'validator')
  .route('retry', 'analyzer')     // Feedback loop
  .route('complete', 'storage')
  .build();
```

## Next Steps

- [Custom Agents](./custom-agents.md) - Create specialized agents for your use case
- [Event System](./event-system.md) - Learn about inter-agent communication
- [Examples](./examples.md) - See complete workflow examples
