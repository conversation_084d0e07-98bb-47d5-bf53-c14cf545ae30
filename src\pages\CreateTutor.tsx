import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import GitHubRepoCrawler from "@/components/GitHubRepoCrawler";
import { Button } from "@/components/ui/button";
import { useTrialStatus } from "@/hooks/useTrialStatus";
import { useMonthlyTutorialUsage } from "@/hooks/useMonthlyTutorialUsage";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, BookOpen, Users, Zap, Target } from "lucide-react";
import { SubscriptionPlans } from "@/components/subscription/SubscriptionPlans";
import { useSubscription } from "@/hooks/useSubscription";
import { Skeleton } from "@/components/ui/skeleton";

interface TutorFormState {
  repoUrl: string;
  isPrivateRepo: boolean;
  githubToken: string;
  selectedFiles: string[];
  targetAudience: "beginner" | "intermediate" | "advanced";
  contentLanguage: string;
  tutorialFormat: "interactive" | "guided" | "self-paced";
  maxConcepts: number;
  includeExercises: boolean;
  includeDiagrams: boolean;
  includeExamples: boolean;
  advancedOptions: {
    cacheDuration: number;
    customPrompts: boolean;
  };
}

const CreateTutor = () => {
  const navigate = useNavigate();
  const { subscribed, loading: subscriptionLoading } = useSubscription();
  const { trialStatus, loading: trialLoading } = useTrialStatus();
  const { monthlyTutorialsCreated, maxTutorialsPerMonth, loading: usageLoading } = useMonthlyTutorialUsage();

  const [formState, setFormState] = useState<TutorFormState>({
    repoUrl: "",
    isPrivateRepo: false,
    githubToken: "",
    selectedFiles: [],
    targetAudience: "beginner",
    contentLanguage: "english",
    tutorialFormat: "guided",
    maxConcepts: 8,
    includeExercises: true,
    includeDiagrams: true,
    includeExamples: true,
    advancedOptions: {
      cacheDuration: 3600,
      customPrompts: false,
    },
  });

  const [isGenerating, setIsGenerating] = useState(false);

  const canGenerate = subscribed || trialStatus.isInTrial;
  const hasReachedLimit = monthlyTutorialsCreated >= maxTutorialsPerMonth;

  const handleInputChange = (field: keyof TutorFormState, value: any) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }));
  };



  const handleGenerateTutorial = async () => {
    if (!formState.repoUrl) {
      toast({
        title: "Repository URL Required",
        description: "Please enter a GitHub repository URL to continue.",
        variant: "destructive",
      });
      return;
    }

    if (formState.selectedFiles.length === 0) {
      toast({
        title: "No Files Selected",
        description: "Please select at least one file to include in your tutorial.",
        variant: "destructive",
      });
      return;
    }

    if (!canGenerate) {
      toast({
        title: "Subscription Required",
        description: "Please subscribe or start a trial to generate tutorials.",
        variant: "destructive",
      });
      return;
    }

    if (hasReachedLimit) {
      toast({
        title: "Usage Limit Reached",
        description: "You've reached your monthly tutorial limit. Please upgrade your plan.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Navigate to tutor creation status page with parameters
      navigate("/dashboard/tutor-creation-status", {
        state: {
          repoUrl: formState.repoUrl,
          isPrivateRepo: formState.isPrivateRepo,
          githubToken: formState.githubToken,
          selectedFiles: formState.selectedFiles,
          targetAudience: formState.targetAudience,
          contentLanguage: formState.contentLanguage,
          tutorialFormat: formState.tutorialFormat,
          maxConcepts: formState.maxConcepts,
          includeExercises: formState.includeExercises,
          includeDiagrams: formState.includeDiagrams,
          includeExamples: formState.includeExamples,
          advancedOptions: formState.advancedOptions,
        },
      });
    } catch (error) {
      console.error("Error starting tutorial generation:", error);
      toast({
        title: "Error",
        description: "Failed to start tutorial generation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  if (subscriptionLoading || trialLoading || usageLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center space-x-3">
          <BookOpen className="h-8 w-8 text-blue-600" />
          <h1 className="text-4xl font-bold text-gray-900">Interactive Tutorial Creator</h1>
        </div>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          Transform your code into engaging, hands-on learning experiences with exercises, 
          quizzes, and step-by-step guidance tailored to your audience.
        </p>
      </div>

      {/* Feature Highlights */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
          <div className="flex items-center space-x-3 mb-3">
            <Target className="h-6 w-6 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Adaptive Learning</h3>
          </div>
          <p className="text-gray-600 text-sm">
            Content automatically adapts to beginner, intermediate, or advanced skill levels
          </p>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-lg border border-green-200">
          <div className="flex items-center space-x-3 mb-3">
            <Zap className="h-6 w-6 text-green-600" />
            <h3 className="font-semibold text-gray-900">Interactive Exercises</h3>
          </div>
          <p className="text-gray-600 text-sm">
            Hands-on coding challenges, quizzes, and debugging exercises for active learning
          </p>
        </div>
        
        <div className="bg-gradient-to-br from-purple-50 to-violet-50 p-6 rounded-lg border border-purple-200">
          <div className="flex items-center space-x-3 mb-3">
            <Users className="h-6 w-6 text-purple-600" />
            <h3 className="font-semibold text-gray-900">Progressive Structure</h3>
          </div>
          <p className="text-gray-600 text-sm">
            Concepts build upon each other with clear learning paths and dependencies
          </p>
        </div>
      </div>

      {/* Usage Status */}
      {!subscribed && (
        <Alert className="border-amber-200 bg-amber-50">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            {trialStatus.isInTrial ? (
              <>Trial active: {trialStatus.daysLeft} days remaining. You can generate {maxTutorialsPerMonth - monthlyTutorialsCreated} more tutorials this month.</>
            ) : (
              <>Your trial has expired. Please subscribe to continue generating tutorials.</>
            )}
          </AlertDescription>
        </Alert>
      )}

      {hasReachedLimit && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            You've reached your monthly limit of {maxTutorialsPerMonth} tutorials. Please upgrade your plan to generate more.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      {!canGenerate ? (
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Subscription Required
          </h2>
          <p className="text-gray-600 mb-8">
            Subscribe to start creating interactive tutorials from your code repositories.
          </p>
          <SubscriptionPlans />
        </div>
      ) : (
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Configuration Panel */}
          <div className="lg:col-span-1 space-y-6">
            {/* Tutorial Settings */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Tutorial Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Target Audience
                  </label>
                  <select
                    value={formState.targetAudience}
                    onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="beginner">Beginner - New to programming concepts</option>
                    <option value="intermediate">Intermediate - Some programming experience</option>
                    <option value="advanced">Advanced - Experienced developers</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tutorial Format
                  </label>
                  <select
                    value={formState.tutorialFormat}
                    onChange={(e) => handleInputChange('tutorialFormat', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="guided">Guided - Step-by-step with explanations</option>
                    <option value="interactive">Interactive - Hands-on with immediate feedback</option>
                    <option value="self-paced">Self-paced - Independent learning modules</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Content Language
                  </label>
                  <select
                    value={formState.contentLanguage}
                    onChange={(e) => handleInputChange('contentLanguage', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="english">English</option>
                    <option value="spanish">Spanish</option>
                    <option value="french">French</option>
                    <option value="german">German</option>
                    <option value="chinese">Chinese</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Max Learning Concepts: {formState.maxConcepts}
                  </label>
                  <input
                    type="range"
                    min="3"
                    max="15"
                    value={formState.maxConcepts}
                    onChange={(e) => handleInputChange('maxConcepts', parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>3 (Quick)</span>
                    <span>15 (Comprehensive)</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Features */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Interactive Features</h3>
              
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formState.includeExercises}
                    onChange={(e) => handleInputChange('includeExercises', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Include coding exercises and challenges</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formState.includeDiagrams}
                    onChange={(e) => handleInputChange('includeDiagrams', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Include visual diagrams and flowcharts</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={formState.includeExamples}
                    onChange={(e) => handleInputChange('includeExamples', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">Include practical code examples</span>
                </label>
              </div>
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerateTutorial}
              disabled={!canGenerate || hasReachedLimit || isGenerating || formState.selectedFiles.length === 0}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg font-semibold"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Creating Tutorial...
                </>
              ) : (
                <>
                  <BookOpen className="h-5 w-5 mr-2" />
                  Create Interactive Tutorial
                </>
              )}
            </Button>
          </div>

          {/* Repository Crawler */}
          <div className="lg:col-span-2">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Repository Configuration</h3>

              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    GitHub Repository URL
                  </label>
                  <input
                    type="url"
                    value={formState.repoUrl}
                    onChange={(e) => handleInputChange('repoUrl', e.target.value)}
                    placeholder="https://github.com/username/repository"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="privateRepo"
                    checked={formState.isPrivateRepo}
                    onChange={(e) => handleInputChange('isPrivateRepo', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="privateRepo" className="text-sm text-gray-700">
                    Private repository (requires GitHub token)
                  </label>
                </div>

                {formState.isPrivateRepo && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      GitHub Personal Access Token
                    </label>
                    <input
                      type="password"
                      value={formState.githubToken}
                      onChange={(e) => handleInputChange('githubToken', e.target.value)}
                      placeholder="ghp_xxxxxxxxxxxxxxxxxxxx"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Required for private repositories. Create one at GitHub Settings → Developer settings → Personal access tokens
                    </p>
                  </div>
                )}
              </div>

              {formState.repoUrl && (
                <GitHubRepoCrawler
                  repoUrl={formState.repoUrl}
                  githubToken={formState.isPrivateRepo ? formState.githubToken : undefined}
                  includePatterns={[
                    "**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx",
                    "**/*.py", "**/*.java", "**/*.go", "**/*.rs",
                    "**/*.cpp", "**/*.c", "**/*.h", "**/*.cs",
                    "**/*.php", "**/*.rb", "**/*.md", "**/*.json",
                    "**/*.yaml", "**/*.yml", "**/Dockerfile", "**/Makefile"
                  ]}
                  excludePatterns={[
                    "**/node_modules/**", "**/dist/**", "**/build/**",
                    "**/.git/**", "**/coverage/**", "**/*.min.js",
                    "**/*.bundle.js", "**/vendor/**", "**/__pycache__/**",
                    "**/*.pyc", "**/target/**", "**/bin/**", "**/obj/**"
                  ]}
                  onSelectionChange={(files) => handleInputChange('selectedFiles', files)}
                  maxFileSize={50} // 50KB limit for tutorial content
                />
              )}
            </div>
          </div>
        </div>
      )}

      {/* Recent Tutorials Preview */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Interactive Tutorials</h3>
          <Link
            to="/dashboard/tutor-gallery"
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            View all →
          </Link>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50 transition-colors">
            <div className="flex items-center mb-2">
              <BookOpen className="h-4 w-4 text-blue-600 mr-2" />
              <h4 className="font-medium text-gray-800">React Hooks Tutorial</h4>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Interactive guide to React Hooks with hands-on exercises
            </p>
            <div className="flex items-center text-xs text-gray-500">
              <span>8 concepts</span>
              <span className="mx-2">•</span>
              <span>12 exercises</span>
              <span className="mx-2">•</span>
              <span>Beginner</span>
            </div>
          </div>
          
          <div className="border border-gray-200 rounded-md p-4 hover:bg-gray-50 transition-colors">
            <div className="flex items-center mb-2">
              <BookOpen className="h-4 w-4 text-green-600 mr-2" />
              <h4 className="font-medium text-gray-800">Node.js API Tutorial</h4>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Build REST APIs with practical coding challenges
            </p>
            <div className="flex items-center text-xs text-gray-500">
              <span>10 concepts</span>
              <span className="mx-2">•</span>
              <span>15 exercises</span>
              <span className="mx-2">•</span>
              <span>Intermediate</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTutor;
