# Getting Started with PangeaFlow

This guide will help you create your first PangeaFlow workflow in just a few minutes. We'll build a simple code analysis workflow that demonstrates the core concepts.

## Prerequisites

- Node.js 16+ and npm/yarn
- TypeScript knowledge (recommended)
- Basic understanding of async/await patterns

## Installation

PangeaFlow is included in the CodeTutor Pro project. If you're starting a new project, you can copy the PangeaFlow source from `src/pangeaflow/`.

```bash
# Install dependencies (if starting fresh)
npm install

# Import PangeaFlow in your project
import { WorkflowBuilder } from '@/pangeaflow';
```

## Your First Workflow

Let's create a simple workflow that analyzes code and generates a summary:

### Step 1: Define Your LLM Provider

```typescript
// Define an LLM provider function
const llmProvider = async (prompt: string, context: Record<string, unknown>) => {
  // Your LLM integration here (OpenAI, Anthropic, etc.)
  const response = await callYourLLM(prompt);
  return response;
};
```

### Step 2: Create Tools

```typescript
// Define tools for your workflow
const tools = {
  'file-reader': async (filePath: string) => {
    // Read file contents
    return await readFile(filePath);
  },
  'code-analyzer': async (code: string) => {
    // Analyze code structure
    return analyzeCodeStructure(code);
  }
};
```

### Step 3: Build Your Workflow

```typescript
import { WorkflowBuilder } from '@/pangeaflow';

const workflow = WorkflowBuilder.create()
  // Add a reasoning agent for planning
  .addReasoningAgent('planner', llmProvider)
  
  // Add a tool agent for file operations
  .addToolAgent('file-processor', tools)
  
  // Add a memory agent for state management
  .addMemoryAgent('memory')
  
  // Define the workflow routes
  .route('start', 'planner')           // Start with planning
  .route('process-files', 'file-processor')  // Process files
  .route('store-results', 'memory')    // Store results
  .route('error', 'planner')          // Handle errors
  .build();
```

### Step 4: Execute Your Workflow

```typescript
// Execute the workflow
const results = await workflow.execute('start', {
  input: 'Analyze the main.ts file',
  filePath: './src/main.ts'
});

console.log('Workflow completed:', results);
```

## Understanding the Flow

Here's what happens when you execute this workflow:

```mermaid
graph LR
    A[Start] --> B[Planner Agent]
    B --> C[File Processor]
    C --> D[Memory Agent]
    D --> E[Complete]
    
    B -.-> F[Error Handler]
    C -.-> F
    F --> B
```

1. **Start**: Workflow begins with the 'start' action
2. **Planner**: ReasoningAgent analyzes the request and decides next steps
3. **File Processor**: ToolAgent executes file operations
4. **Memory**: MemoryAgent stores results and manages state
5. **Error Handling**: Any errors are routed back to the planner

## Event Monitoring

PangeaFlow provides built-in event monitoring:

```typescript
// Listen to workflow events
workflow.on('agent.status', (event) => {
  console.log(`Agent ${event.source}: ${event.payload.message}`);
});

workflow.on('step.completed', (event) => {
  console.log('Step completed:', event.payload);
});

workflow.on('error', (event) => {
  console.error('Workflow error:', event.payload);
});
```

## Getting Metrics

Monitor your workflow performance:

```typescript
// Get workflow metrics
const metrics = workflow.getMetrics();
console.log('Performance metrics:', {
  totalExecutions: metrics.totalExecutions,
  averageDuration: metrics.averageDuration,
  successRate: metrics.successRate
});
```

## Next Steps

Now that you have a basic workflow running, explore these advanced features:

1. **[Core Concepts](./core-concepts.md)** - Understand the architecture deeply
2. **[Built-in Agents](./built-in-agents.md)** - Learn about ReasoningAgent, ToolAgent, and MemoryAgent
3. **[Custom Agents](./custom-agents.md)** - Create specialized agents for your use case
4. **[Event System](./event-system.md)** - Master the event-driven architecture
5. **[Examples](./examples.md)** - See real-world workflow patterns

## Common Patterns

### Sequential Processing
```typescript
const sequential = WorkflowBuilder.create()
  .addReasoningAgent('step1', llmProvider)
  .addReasoningAgent('step2', llmProvider)
  .addReasoningAgent('step3', llmProvider)
  .route('start', 'step1')
  .route('next', 'step2')
  .route('final', 'step3')
  .build();
```

### Parallel Processing
```typescript
const parallel = WorkflowBuilder.create()
  .addToolAgent('worker1', tools)
  .addToolAgent('worker2', tools)
  .addMemoryAgent('aggregator')
  .route('start', 'worker1', 'worker2')  // Multiple components
  .route('combine', 'aggregator')
  .build();
```

### Error Recovery
```typescript
const resilient = WorkflowBuilder.create()
  .addReasoningAgent('main', llmProvider)
  .addReasoningAgent('fallback', llmProvider)
  .route('start', 'main')
  .route('retry', 'main')
  .route('error', 'fallback')  // Automatic error handling
  .build();
```

Ready to dive deeper? Continue with [Core Concepts](./core-concepts.md) to understand PangeaFlow's architecture and design principles.
