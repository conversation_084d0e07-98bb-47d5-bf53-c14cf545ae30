# PangeaFlow Documentation

Welcome to PangeaFlow - a next-generation agentic AI workflow system designed for building scalable, reactive AI agent workflows with built-in observability, error recovery, and dynamic orchestration.

## What is PangeaFlow?

PangeaFlow is a reactive, event-driven framework that provides significant improvements over traditional linear workflow approaches:

- **Event-driven architecture** for loose coupling between components
- **Reactive state management** with automatic invalidation
- **Built-in telemetry and observability** for monitoring and debugging
- **Dynamic workflow adaptation** based on runtime conditions
- **Memory-efficient streaming** for large datasets
- **Hierarchical error boundaries** with recovery strategies

## Quick Navigation

### Getting Started
- [🚀 Getting Started Guide](./getting-started.md) - Set up your first PangeaFlow workflow
- [📖 Core Concepts](./core-concepts.md) - Understand the architecture and philosophy
- [🔧 API Reference](./api-reference.md) - Complete API documentation

### Building Workflows
- [🏗️ Workflow Builder](./workflow-builder.md) - Learn the WorkflowBuilder patterns
- [🤖 Built-in Agents](./built-in-agents.md) - ReasoningAgent, ToolAgent, MemoryAgent
- [⚙️ Custom Agents](./custom-agents.md) - Create your own agent components

### Advanced Features
- [📡 Event System](./event-system.md) - Event-driven architecture and handling
- [📊 Telemetry & Monitoring](./telemetry-monitoring.md) - Observability and performance
- [🛡️ Error Handling](./error-handling.md) - Error recovery and resilience
- [🌊 Streaming & Batch](./streaming-batch.md) - Handle large datasets efficiently

### Integration & Examples
- [🔗 Integration Patterns](./integration-patterns.md) - LangGraph and system integration
- [💡 Examples](./examples.md) - Practical examples and use cases
- [🔧 Troubleshooting](./troubleshooting.md) - Common issues and solutions
- [📈 Migration Guide](./migration-guide.md) - Migrate from PocketFlow

## Key Features at a Glance

### 🎯 **Reactive Workflows**
Build workflows that automatically adapt to changing conditions and state updates.

### 🔄 **Event-Driven**
Loose coupling between components through a powerful event system.

### 📈 **Built-in Observability**
Comprehensive telemetry, metrics, and monitoring out of the box.

### 🛡️ **Error Resilience**
Hierarchical error boundaries with automatic recovery strategies.

### 🌊 **Streaming Support**
Memory-efficient processing of large datasets with streaming capabilities.

### 🤖 **Rich Agent Ecosystem**
Pre-built agents for reasoning, tool execution, and memory management.

## Architecture Overview

```mermaid
graph TB
    subgraph "PangeaFlow Architecture"
        WB[WorkflowBuilder] --> WO[WorkflowOrchestrator]
        WO --> EB[EventBus]
        WO --> TC[TelemetryCollector]
        WO --> AG[Agent Components]
        
        subgraph "Built-in Agents"
            RA[ReasoningAgent]
            TA[ToolAgent]
            MA[MemoryAgent]
        end
        
        subgraph "Custom Agents"
            CA[Custom Agent 1]
            CB[Custom Agent 2]
        end
        
        AG --> RA
        AG --> TA
        AG --> MA
        AG --> CA
        AG --> CB
        
        EB --> EL[Event Listeners]
        TC --> MT[Metrics & Telemetry]
    end
```

## Quick Start Example

```typescript
import { WorkflowBuilder } from '@/pangeaflow';

// Create a simple workflow
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('planner', llmProvider)
  .addToolAgent('executor', tools)
  .addMemoryAgent('storage')
  .route('start', 'planner')
  .route('execute', 'executor')
  .route('store', 'storage')
  .build();

// Execute the workflow
const results = await workflow.execute('start', {
  input: 'Analyze this code repository'
});
```

## Community and Support

- **GitHub Issues**: Report bugs and request features
- **Documentation**: Comprehensive guides and API reference
- **Examples**: Real-world usage patterns and best practices

---

Ready to get started? Check out our [Getting Started Guide](./getting-started.md) to build your first PangeaFlow workflow!
