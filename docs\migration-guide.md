# Migration Guide: PocketFlow to PangeaFlow

This guide helps you migrate existing PocketFlow workflows to PangeaFlow, taking advantage of the new reactive, event-driven architecture while preserving your existing logic.

## Key Differences

### Architecture Comparison

| Aspect | PocketFlow | PangeaFlow |
|--------|------------|------------|
| **Execution Model** | Linear node chains | Event-driven orchestration |
| **State Management** | Shared store passed between nodes | Reactive state with automatic invalidation |
| **Communication** | Direct method calls | Event bus messaging |
| **Error Handling** | Manual try-catch in nodes | Built-in error boundaries and recovery |
| **Monitoring** | External logging | Built-in telemetry and observability |
| **Scalability** | Single-threaded execution | Parallel and streaming support |

### Conceptual Mapping

| PocketFlow Concept | PangeaFlow Equivalent |
|-------------------|----------------------|
| `Node` | `AgentComponent` |
| `Flow` | `WorkflowOrchestrator` |
| `SharedStore` | `ExecutionContext.metadata` + `ReactiveState` |
| `Action` | `ExecutionResult.nextActions` |
| `next()` method | Route definitions |

## Migration Steps

### Step 1: Analyze Your PocketFlow Workflow

First, understand your existing PocketFlow structure:

```typescript
// Example PocketFlow workflow
class FetchRepo extends Node {
  async exec(args: string): Promise<string> {
    const parsedArgs = JSON.parse(args);
    const files = await fetchGitHubFiles(parsedArgs.repo_url);
    return JSON.stringify({ files });
  }
  
  async post(shared: SharedStore, prepRes: any, execRes: string): Promise<Action> {
    const { files } = JSON.parse(execRes);
    shared.files = files;
    return 'analyze';
  }
}

class AnalyzeCode extends Node {
  async exec(prepRes: any): Promise<any> {
    const { files } = prepRes;
    const analysis = await analyzeCodeStructure(files);
    return analysis;
  }
  
  async post(shared: SharedStore, prepRes: any, execRes: any): Promise<Action> {
    shared.analysis = execRes;
    return 'generate-docs';
  }
}

// Flow setup
const fetchRepo = new FetchRepo();
const analyzeCode = new AnalyzeCode();
const generateDocs = new GenerateDocs();

fetchRepo.next(analyzeCode);
analyzeCode.next(generateDocs);

const flow = new Flow(fetchRepo);
```

### Step 2: Convert Nodes to Agents

Transform each PocketFlow node into a PangeaFlow agent:

```typescript
// Convert FetchRepo node to PangeaFlow agent
class RepoFetchAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('repo-fetch', eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      // Extract data from context (equivalent to PocketFlow args)
      const { repo_url } = context.metadata;
      
      // Emit status (new capability in PangeaFlow)
      this.emit('agent.status', {
        agentName: 'RepoFetchAgent',
        status: 'fetching',
        message: `Fetching files from ${repo_url}`
      });
      
      // Execute the main logic (similar to PocketFlow exec)
      const files = await this.withTelemetry('fetch-files', async () => {
        return await fetchGitHubFiles(repo_url);
      });
      
      // Update shared data and determine next action (similar to PocketFlow post)
      return {
        success: true,
        data: { files },
        events: [],
        nextActions: ['analyze'], // Equivalent to returning 'analyze' action
        metadata: { ...context.metadata, files } // Update shared state
      };
      
    } catch (error) {
      this.emit('agent.error', {
        agentName: 'RepoFetchAgent',
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}

// Convert AnalyzeCode node
class CodeAnalysisAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('code-analysis', eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const { files } = context.metadata;
      
      this.emit('agent.status', {
        agentName: 'CodeAnalysisAgent',
        status: 'analyzing',
        message: `Analyzing ${files.length} files`
      });
      
      const analysis = await this.withTelemetry('code-analysis', async () => {
        return await analyzeCodeStructure(files);
      });
      
      return {
        success: true,
        data: { analysis },
        events: [],
        nextActions: ['generate-docs'],
        metadata: { ...context.metadata, analysis }
      };
      
    } catch (error) {
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
```

### Step 3: Replace Flow with WorkflowBuilder

Convert your PocketFlow flow to a PangeaFlow workflow:

```typescript
// PocketFlow flow setup
const flow = new Flow(fetchRepo);

// PangeaFlow workflow setup
function createMigratedWorkflow() {
  const workflow = WorkflowBuilder.create()
    // Add memory agent for shared state management
    .addMemoryAgent('shared-state')
    
    // Define routes (equivalent to node.next() calls)
    .route('start', 'repo-fetch')      // Start with repo fetch
    .route('analyze', 'code-analysis') // fetchRepo -> analyzeCode
    .route('generate-docs', 'doc-generator') // analyzeCode -> generateDocs
    .route('complete', 'shared-state') // Store final results
    .route('error', 'error-handler')   // Error handling
    .build();
  
  // Register custom agents
  const orchestrator = workflow as any;
  
  const repoAgent = new RepoFetchAgent(
    orchestrator.eventBus,
    orchestrator.telemetry
  );
  orchestrator.registerComponent(repoAgent);
  
  const analysisAgent = new CodeAnalysisAgent(
    orchestrator.eventBus,
    orchestrator.telemetry
  );
  orchestrator.registerComponent(analysisAgent);
  
  return workflow;
}
```

### Step 4: Update Execution Pattern

Change how you execute the workflow:

```typescript
// PocketFlow execution
const shared: SharedStore = {
  repo_url: 'owner/repo',
  selected_files: ['src/main.ts']
};

const result = await flow.run(shared);

// PangeaFlow execution
const workflow = createMigratedWorkflow();

// Set up event monitoring (new capability)
workflow.on('agent.status', (event) => {
  console.log(`${event.payload.agentName}: ${event.payload.message}`);
});

const results = await workflow.execute('start', {
  metadata: {
    repo_url: 'owner/repo',
    selected_files: ['src/main.ts']
  }
});
```

## Common Migration Patterns

### 1. Shared Store to Reactive State

**PocketFlow:**
```typescript
// Shared store passed between nodes
interface SharedStore {
  files: any[];
  analysis: any;
  documentation: string;
}

class MyNode extends Node {
  async post(shared: SharedStore, prepRes: any, execRes: any): Promise<Action> {
    shared.analysis = execRes; // Direct mutation
    return 'next';
  }
}
```

**PangeaFlow:**
```typescript
// State managed through ExecutionContext and ReactiveState
class MyAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const analysis = await this.performAnalysis();
    
    // State is passed through metadata
    return {
      success: true,
      data: { analysis },
      events: [],
      nextActions: ['next'],
      metadata: { ...context.metadata, analysis } // Immutable update
    };
  }
}
```

### 2. Error Handling

**PocketFlow:**
```typescript
class MyNode extends Node {
  async exec(prepRes: any): Promise<any> {
    try {
      return await this.doWork();
    } catch (error) {
      console.error('Error:', error);
      throw error; // Manual error handling
    }
  }
}
```

**PangeaFlow:**
```typescript
class MyAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      const result = await this.doWork();
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: {}
      };
    } catch (error) {
      // Built-in error handling with events
      this.emit('agent.error', {
        agentName: 'MyAgent',
        error: error.message
      });
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'], // Automatic error routing
        metadata: {}
      };
    }
  }
}
```

### 3. Conditional Routing

**PocketFlow:**
```typescript
class ConditionalNode extends Node {
  async post(shared: SharedStore, prepRes: any, execRes: any): Promise<Action> {
    if (execRes.needsReview) {
      return 'review';
    } else {
      return 'publish';
    }
  }
}
```

**PangeaFlow:**
```typescript
class ConditionalAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const result = await this.processData();
    
    // Conditional routing through nextActions
    const nextActions = result.needsReview ? ['review'] : ['publish'];
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions,
      metadata: { ...context.metadata, result }
    };
  }
}
```

## Advanced Migration Scenarios

### 1. Batch Processing

**PocketFlow:**
```typescript
class BatchNode extends BatchNode {
  async exec(items: any[]): Promise<any[]> {
    return await Promise.all(items.map(item => this.processItem(item)));
  }
}
```

**PangeaFlow:**
```typescript
// Use StreamingWorkflow for batch processing
import { StreamingWorkflow } from '@/pangeaflow';

async function migrateBatchProcessing(workflow: WorkflowOrchestrator, items: any[]) {
  const streamingWorkflow = new StreamingWorkflow(workflow);
  
  const results = [];
  for await (const batchResults of streamingWorkflow.processStream(
    items[Symbol.asyncIterator](),
    'start',
    10 // batch size
  )) {
    results.push(...batchResults);
  }
  
  return results;
}
```

### 2. Parallel Processing

**PocketFlow:**
```typescript
class ParallelNode extends ParallelNode {
  async exec(data: any): Promise<any> {
    const [result1, result2] = await Promise.all([
      this.processA(data),
      this.processB(data)
    ]);
    
    return { result1, result2 };
  }
}
```

**PangeaFlow:**
```typescript
// Use parallel routing
const workflow = WorkflowBuilder.create()
  .addToolAgent('processor-a', toolsA)
  .addToolAgent('processor-b', toolsB)
  .addMemoryAgent('aggregator')
  
  .route('start', 'processor-a', 'processor-b') // Parallel execution
  .route('aggregate', 'aggregator')
  .build();
```

## Migration Checklist

### Pre-Migration
- [ ] Document existing PocketFlow workflow structure
- [ ] Identify all nodes and their responsibilities
- [ ] Map data flow between nodes
- [ ] Note error handling patterns
- [ ] Identify performance bottlenecks

### During Migration
- [ ] Convert each node to an agent
- [ ] Replace Flow with WorkflowBuilder
- [ ] Update shared store usage
- [ ] Add event monitoring
- [ ] Implement error handling
- [ ] Add telemetry integration

### Post-Migration
- [ ] Test workflow execution
- [ ] Verify data flow
- [ ] Check error handling
- [ ] Monitor performance
- [ ] Add comprehensive logging
- [ ] Update documentation

## Benefits After Migration

### 1. Enhanced Observability
```typescript
// Monitor workflow performance
workflow.on('step.completed', (event) => {
  const metrics = workflow.getMetrics();
  console.log('Performance:', {
    duration: metrics.averageDuration,
    successRate: metrics.successRate
  });
});
```

### 2. Better Error Recovery
```typescript
// Automatic retry and fallback
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('primary', primaryLLM)
  .addReasoningAgent('fallback', fallbackLLM)
  .route('start', 'primary')
  .route('retry', 'primary')
  .route('error', 'fallback')
  .build();
```

### 3. Real-time Updates
```typescript
// Real-time progress updates
workflow.on('agent.status', (event) => {
  updateUI({
    agent: event.payload.agentName,
    progress: event.payload.progress,
    message: event.payload.message
  });
});
```

## Troubleshooting Migration Issues

### Common Problems

1. **State not persisting between agents**
   - Ensure metadata is properly passed through ExecutionResult
   - Use MemoryAgent for persistent state

2. **Events not firing**
   - Check event type names for typos
   - Ensure listeners are registered before execution

3. **Performance degradation**
   - Use telemetry to identify bottlenecks
   - Consider streaming for large datasets

4. **Complex routing not working**
   - Break down complex logic into smaller agents
   - Use conditional routing in agent execution

## Next Steps

After successful migration:

1. **Optimize Performance**: Use PangeaFlow's streaming and parallel processing capabilities
2. **Add Monitoring**: Implement comprehensive telemetry and alerting
3. **Enhance Error Handling**: Take advantage of built-in error recovery
4. **Scale Horizontally**: Use event-driven architecture for better scalability

## Getting Help

If you encounter issues during migration:

1. Check the [Troubleshooting Guide](./troubleshooting.md)
2. Review [Examples](./examples.md) for similar patterns
3. Consult the [API Reference](./api-reference.md) for detailed documentation

The migration from PocketFlow to PangeaFlow unlocks powerful new capabilities while preserving your existing workflow logic. Take advantage of the reactive, event-driven architecture to build more robust and scalable AI workflows.
