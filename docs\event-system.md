# Event System

PangeaFlow's event-driven architecture enables loose coupling between components, real-time monitoring, and reactive workflows. The event system is central to how components communicate and coordinate their activities.

## Core Concepts

### EventBus

The EventBus is the central communication hub that manages all event publishing and subscription:

```typescript
class EventBus {
  // Emit events to all listeners
  emit<T>(type: string, payload: T, source: ComponentId, correlationId?: string): void;
  
  // Subscribe to events
  on(type: string, listener: (event: WorkflowEvent) => void): () => void;
}
```

### WorkflowEvent

All events follow a consistent structure:

```typescript
interface WorkflowEvent<T = unknown> {
  id: string;                    // Unique event ID
  type: string;                  // Event type (e.g., 'agent.status')
  timestamp: number;             // When the event occurred
  source: ComponentId;           // Which component emitted it
  payload: T;                    // Event data
  correlationId?: string;        // For request tracing
}
```

## Standard Event Types

### Agent Lifecycle Events

```typescript
// Agent status updates
'agent.status'     // Agent status changes
'agent.started'    // Agent execution started
'agent.completed'  // Agent execution completed
'agent.failed'     // Agent execution failed
'agent.error'      // Agent encountered an error

// Step-level events
'step.started'     // Workflow step started
'step.completed'   // Workflow step completed
'step.failed'      // Workflow step failed

// Workflow-level events
'workflow.started'    // Workflow execution started
'workflow.completed'  // Workflow execution completed
'workflow.failed'     // Workflow execution failed
```

### Data Events

```typescript
// Data processing events
'data.received'    // New data received
'data.processed'   // Data processing completed
'data.validated'   // Data validation completed
'data.stored'      // Data storage completed

// State events
'state.updated'    // Component state updated
'state.synced'     // State synchronized across components
```

### Custom Domain Events

```typescript
// Code analysis events
'code.analyzed'    // Code analysis completed
'concepts.extracted' // Concepts extracted from code
'tutorial.generated' // Tutorial generation completed

// File processing events
'files.fetched'    // Files retrieved from repository
'files.processed'  // File processing completed
'files.validated'  // File validation completed
```

## Emitting Events

### From Agent Components

Agents can emit events using the protected `emit` method:

```typescript
class MyAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // Emit status update
    this.emit('agent.status', {
      agentName: 'MyAgent',
      status: 'processing',
      progress: 50,
      message: 'Processing data...'
    });
    
    // Perform work
    const result = await this.performWork();
    
    // Emit completion event
    this.emit('work.completed', {
      agentId: this.id,
      result,
      completedAt: Date.now()
    });
    
    return {
      success: true,
      data: result,
      events: [],
      nextActions: ['continue'],
      metadata: {}
    };
  }
}
```

### Event Payload Examples

#### Agent Status Events

```typescript
this.emit('agent.status', {
  agentName: 'RepoAnalysisAgent',
  status: 'processing',
  progress: 75,
  message: 'Analyzing repository structure',
  metadata: {
    filesProcessed: 15,
    totalFiles: 20
  }
});
```

#### Error Events

```typescript
this.emit('agent.error', {
  agentName: 'ContentGenerationAgent',
  error: 'LLM API rate limit exceeded',
  stage: 'content-generation',
  retryable: true,
  retryAfter: 60000 // milliseconds
});
```

#### Data Processing Events

```typescript
this.emit('data.processed', {
  dataType: 'code-files',
  inputCount: 50,
  outputCount: 45,
  processingTime: 2500,
  skippedFiles: ['test.tmp', 'cache.dat']
});
```

## Listening to Events

### Workflow-Level Listeners

Set up listeners when creating workflows:

```typescript
const workflow = WorkflowBuilder.create()
  .addReasoningAgent('analyzer', llmProvider)
  .addToolAgent('processor', tools)
  .build();

// Listen to agent status updates
workflow.on('agent.status', (event) => {
  const { agentName, status, progress, message } = event.payload;
  console.log(`${agentName}: ${status} (${progress}%) - ${message}`);
  
  // Update UI or external systems
  updateProgressBar(agentName, progress);
});

// Listen to errors
workflow.on('agent.error', (event) => {
  const { agentName, error, retryable } = event.payload;
  console.error(`Error in ${agentName}: ${error}`);
  
  if (retryable) {
    // Implement retry logic
    scheduleRetry(agentName);
  } else {
    // Handle non-retryable errors
    notifyUser(`Fatal error in ${agentName}: ${error}`);
  }
});

// Listen to workflow completion
workflow.on('workflow.completed', (event) => {
  const { results, duration } = event.payload;
  console.log(`Workflow completed in ${duration}ms`);
  
  // Process final results
  handleWorkflowResults(results);
});
```

### Component-Level Listeners

Components can listen to events from other components:

```typescript
class CoordinatorAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('coordinator', eventBus, telemetry);
    
    // Listen to data processing events
    this.eventBus.on('data.processed', this.handleDataProcessed.bind(this));
    
    // Listen to agent completion events
    this.eventBus.on('agent.completed', this.handleAgentCompleted.bind(this));
    
    // Listen to error events
    this.eventBus.on('agent.error', this.handleAgentError.bind(this));
  }
  
  private async handleDataProcessed(event: WorkflowEvent) {
    const { dataType, outputCount } = event.payload;
    console.log(`Processed ${outputCount} items of type ${dataType}`);
    
    // Coordinate next steps based on processed data
    if (outputCount > 0) {
      this.emit('coordination.trigger', {
        action: 'continue-processing',
        source: event.source,
        timestamp: Date.now()
      });
    }
  }
  
  private async handleAgentCompleted(event: WorkflowEvent) {
    const { agentId, result } = event.payload;
    
    // Track completion status
    this.updateCompletionStatus(agentId, result);
    
    // Check if all agents are complete
    if (this.allAgentsComplete()) {
      this.emit('workflow.ready-to-finalize', {
        completedAgents: this.getCompletedAgents(),
        finalizeAt: Date.now()
      });
    }
  }
}
```

## Event Patterns

### Request-Response Pattern

Implement request-response communication using events:

```typescript
class RequestorAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    const requestId = crypto.randomUUID();
    
    // Send request
    this.emit('data.request', {
      requestId,
      requestType: 'file-analysis',
      parameters: { filePath: '/src/main.ts' }
    });
    
    // Wait for response (in real implementation, use proper async handling)
    return new Promise((resolve) => {
      const responseHandler = (event: WorkflowEvent) => {
        const { requestId: responseRequestId, result } = event.payload;
        
        if (responseRequestId === requestId) {
          // Clean up listener
          this.eventBus.off('data.response', responseHandler);
          
          resolve({
            success: true,
            data: result,
            events: [],
            nextActions: ['continue'],
            metadata: {}
          });
        }
      };
      
      this.eventBus.on('data.response', responseHandler);
    });
  }
}

class ResponderAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('responder', eventBus, telemetry);
    
    // Listen for requests
    this.eventBus.on('data.request', this.handleRequest.bind(this));
  }
  
  private async handleRequest(event: WorkflowEvent) {
    const { requestId, requestType, parameters } = event.payload;
    
    try {
      // Process the request
      const result = await this.processRequest(requestType, parameters);
      
      // Send response
      this.emit('data.response', {
        requestId,
        result,
        success: true
      });
    } catch (error) {
      // Send error response
      this.emit('data.response', {
        requestId,
        error: error.message,
        success: false
      });
    }
  }
}
```

### Pub-Sub Pattern

Implement publish-subscribe for broadcasting updates:

```typescript
class PublisherAgent extends AgentComponent {
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    // Process data
    const processedData = await this.processData(context.metadata);
    
    // Publish to all subscribers
    this.emit('data.published', {
      topic: 'code-analysis-results',
      data: processedData,
      publishedAt: Date.now(),
      publisher: this.id
    });
    
    return {
      success: true,
      data: processedData,
      events: [],
      nextActions: ['complete'],
      metadata: {}
    };
  }
}

class SubscriberAgent extends AgentComponent {
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('subscriber', eventBus, telemetry);
    
    // Subscribe to published data
    this.eventBus.on('data.published', this.handlePublishedData.bind(this));
  }
  
  private async handlePublishedData(event: WorkflowEvent) {
    const { topic, data, publisher } = event.payload;
    
    if (topic === 'code-analysis-results') {
      // Process the published data
      await this.processAnalysisResults(data);
      
      // Acknowledge receipt
      this.emit('data.acknowledged', {
        topic,
        publisher,
        subscriber: this.id,
        acknowledgedAt: Date.now()
      });
    }
  }
}
```

### Event Aggregation

Collect and aggregate events from multiple sources:

```typescript
class AggregatorAgent extends AgentComponent {
  private pendingEvents = new Map<string, any[]>();
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('aggregator', eventBus, telemetry);
    
    // Listen to events from multiple sources
    this.eventBus.on('worker.completed', this.handleWorkerCompleted.bind(this));
    this.eventBus.on('analysis.completed', this.handleAnalysisCompleted.bind(this));
  }
  
  private async handleWorkerCompleted(event: WorkflowEvent) {
    const correlationId = event.correlationId || 'default';
    
    if (!this.pendingEvents.has(correlationId)) {
      this.pendingEvents.set(correlationId, []);
    }
    
    this.pendingEvents.get(correlationId)!.push({
      type: 'worker',
      data: event.payload,
      timestamp: event.timestamp
    });
    
    // Check if we have all required events
    await this.checkForCompletion(correlationId);
  }
  
  private async checkForCompletion(correlationId: string) {
    const events = this.pendingEvents.get(correlationId) || [];
    const workerEvents = events.filter(e => e.type === 'worker');
    const analysisEvents = events.filter(e => e.type === 'analysis');
    
    // If we have all required events, aggregate and emit
    if (workerEvents.length >= 3 && analysisEvents.length >= 1) {
      const aggregatedResult = this.aggregateResults(events);
      
      this.emit('aggregation.completed', {
        correlationId,
        aggregatedResult,
        eventCount: events.length,
        completedAt: Date.now()
      });
      
      // Clean up
      this.pendingEvents.delete(correlationId);
    }
  }
}
```

## Error Handling in Events

### Error Event Structure

```typescript
interface ErrorEvent {
  agentName: string;
  error: string;
  stage?: string;
  retryable: boolean;
  retryAfter?: number;
  context?: Record<string, unknown>;
  stackTrace?: string;
}
```

### Error Recovery Patterns

```typescript
class ErrorHandlerAgent extends AgentComponent {
  private retryAttempts = new Map<string, number>();
  private maxRetries = 3;
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('error-handler', eventBus, telemetry);
    
    this.eventBus.on('agent.error', this.handleAgentError.bind(this));
  }
  
  private async handleAgentError(event: WorkflowEvent) {
    const { agentName, error, retryable, retryAfter } = event.payload;
    const errorKey = `${agentName}-${event.correlationId}`;
    
    if (retryable) {
      const attempts = this.retryAttempts.get(errorKey) || 0;
      
      if (attempts < this.maxRetries) {
        // Schedule retry
        this.retryAttempts.set(errorKey, attempts + 1);
        
        setTimeout(() => {
          this.emit('agent.retry', {
            agentName,
            attempt: attempts + 1,
            maxAttempts: this.maxRetries,
            originalError: error
          });
        }, retryAfter || 1000 * Math.pow(2, attempts)); // Exponential backoff
        
      } else {
        // Max retries exceeded
        this.emit('agent.failed-permanently', {
          agentName,
          finalError: error,
          totalAttempts: attempts + 1
        });
        
        this.retryAttempts.delete(errorKey);
      }
    } else {
      // Non-retryable error
      this.emit('agent.failed-permanently', {
        agentName,
        finalError: error,
        retryable: false
      });
    }
  }
}
```

## Best Practices

### Event Naming

Use consistent, descriptive event names:

```typescript
// Good
'agent.status'
'data.processed'
'workflow.completed'
'file.uploaded'
'user.authenticated'

// Avoid
'update'
'done'
'event1'
'status'
```

### Event Payload Design

Keep payloads consistent and informative:

```typescript
// Good - structured and informative
this.emit('file.processed', {
  fileName: 'main.ts',
  fileSize: 1024,
  processingTime: 150,
  linesOfCode: 45,
  language: 'typescript'
});

// Avoid - minimal or inconsistent structure
this.emit('file.processed', 'main.ts');
```

### Memory Management

Clean up event listeners to prevent memory leaks:

```typescript
class MyAgent extends AgentComponent {
  private listeners: (() => void)[] = [];
  
  constructor(eventBus: EventBus, telemetry: TelemetryCollector) {
    super('my-agent', eventBus, telemetry);
    
    // Store cleanup functions
    this.listeners.push(
      this.eventBus.on('data.received', this.handleData.bind(this))
    );
  }
  
  cleanup() {
    // Clean up all listeners
    this.listeners.forEach(cleanup => cleanup());
    this.listeners = [];
  }
}
```

## Next Steps

- [Telemetry & Monitoring](./telemetry-monitoring.md) - Monitor events and performance
- [Error Handling](./error-handling.md) - Comprehensive error handling strategies
- [Examples](./examples.md) - See event-driven workflow examples
