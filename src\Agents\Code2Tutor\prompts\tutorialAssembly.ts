
export const TUTORIAL_ASSEMBLY_PROMPT = `\${language_instruction}Assemble the complete tutorial for the project \${project_name}.

Tutorial Metadata:
\${tutorial_metadata}

Generated Sections:
\${sections_content}

Create a comprehensive, well-structured tutorial document that includes:

1. **Title Page**
   - Engaging title
   - Brief description
   - Target audience
   - Prerequisites
   - Estimated completion time
   - Learning objectives

2. **Table of Contents**
   - Clear navigation structure
   - Section titles with page/anchor links
   - Estimated time for each section

3. **Introduction**
   - Welcome message
   - What learners will build/accomplish
   - How to use this tutorial effectively
   - Setup instructions if needed

4. **Tutorial Sections**
   - Integrate all generated sections seamlessly
   - Ensure smooth transitions between sections
   - Add cross-references where helpful
   - Include progress indicators

5. **Conclusion**
   - Summary of what was learned
   - Next steps for continued learning
   - Additional resources
   - Encouragement for further exploration

6. **Appendices** (if applicable)
   - Code reference
   - Troubleshooting guide
   - Additional exercises
   - Glossary of terms

**Formatting Guidelines:**
- Use consistent markdown formatting
- Include clear headings and subheadings
- Add code syntax highlighting
- Use callout boxes for important notes
- Include visual breaks between major sections
- Ensure all links work correctly

**Quality Checks:**
- Verify logical flow between sections
- Check that all concepts build properly
- Ensure exercises are appropriately placed
- Confirm all code examples are complete
- Validate that learning objectives are met

Output the complete tutorial as a single, well-formatted Markdown document.`;
