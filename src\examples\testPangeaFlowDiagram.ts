/**
 * Test script to verify the PangeaFlow diagram generator works with real workflows
 */

import { createCode2TutorFlow } from '../Agents/Code2Tutor/flow/pangeaFlow';
import { generatePangeaFlowDiagram } from '../pangeaflow/utils/workflowDiagramGenerator';

/**
 * Test the diagram generator with the actual Code2Tutor workflow
 */
async function testCode2TutorDiagram() {
  console.log('🧪 Testing PangeaFlow diagram generator with Code2Tutor workflow...');

  try {
    // Create the actual Code2Tutor workflow
    const workflow = createCode2TutorFlow();
    
    console.log('✅ Code2Tutor workflow created successfully');

    // Generate diagram with default styling
    await generatePangeaFlowDiagram(
      workflow,
      './output/code2tutor-workflow.png'
    );
    console.log('✅ Default diagram generated: ./output/code2tutor-workflow.png');

    // Generate diagram with custom styling for better visibility
    await generatePangeaFlowDiagram(
      workflow,
      './output/code2tutor-workflow-styled.png',
      {
        width: 1600,
        height: 1200,
        backgroundColor: '#f8fafc',
        componentColor: '#dbeafe',
        actionColor: '#fef3c7',
        edgeColor: '#3b82f6',
        textColor: '#1e293b',
        fontSize: 14,
        padding: 60,
        showComponentTypes: true,
        showActionLabels: true,
      }
    );
    console.log('✅ Styled diagram generated: ./output/code2tutor-workflow-styled.png');

    // Generate a compact version
    await generatePangeaFlowDiagram(
      workflow,
      './output/code2tutor-workflow-compact.png',
      {
        width: 1000,
        height: 700,
        backgroundColor: '#ffffff',
        componentColor: '#f1f5f9',
        actionColor: '#f8fafc',
        edgeColor: '#64748b',
        textColor: '#334155',
        fontSize: 11,
        padding: 40,
        showComponentTypes: false,
        showActionLabels: false,
      }
    );
    console.log('✅ Compact diagram generated: ./output/code2tutor-workflow-compact.png');

    console.log('\n🎉 All Code2Tutor workflow diagrams generated successfully!');
    console.log('📁 Check the ./output/ directory for the generated PNG files');
    console.log('\n📋 Generated files:');
    console.log('   - code2tutor-workflow.png (default styling)');
    console.log('   - code2tutor-workflow-styled.png (custom styling)');
    console.log('   - code2tutor-workflow-compact.png (compact version)');

  } catch (error) {
    console.error('❌ Error testing diagram generator:', error);
    
    // Provide helpful debugging information
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.split('\n').slice(0, 5).join('\n')
      });
    }
  }
}

/**
 * Test with a minimal workflow to ensure basic functionality
 */
async function testMinimalWorkflow() {
  console.log('\n🧪 Testing with minimal workflow...');

  try {
    const { WorkflowBuilder } = await import('../pangeaflow/pangeaflow');

    // Create a simple test workflow
    const workflow = WorkflowBuilder.create()
      .addReasoningAgent('test-agent', async (prompt: string) => `Test response for: ${prompt}`)
      .addMemoryAgent('test-memory')
      .route('start', 'test-agent')
      .route('store', 'test-memory')
      .build();

    await generatePangeaFlowDiagram(
      workflow,
      './output/minimal-test-workflow.png',
      {
        width: 800,
        height: 400,
        backgroundColor: '#f0f9ff',
        componentColor: '#e0f2fe',
        actionColor: '#ecfdf5',
        edgeColor: '#0ea5e9',
        textColor: '#0c4a6e',
        fontSize: 12,
      }
    );

    console.log('✅ Minimal workflow diagram generated: ./output/minimal-test-workflow.png');

  } catch (error) {
    console.error('❌ Error with minimal workflow test:', error);
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🚀 Starting PangeaFlow diagram generator tests...\n');

  // Test with minimal workflow first
  await testMinimalWorkflow();

  // Test with the actual Code2Tutor workflow
  await testCode2TutorDiagram();

  console.log('\n✨ All tests completed!');
}

// Run tests if this file is executed directly
// Check if this is the main module in ES modules
const isMainModule = import.meta.url === `file://${process.argv[1]}`;
if (isMainModule) {
  runTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

export { testCode2TutorDiagram, testMinimalWorkflow, runTests };
