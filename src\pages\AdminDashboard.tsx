
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

import { getUsageStats, getRecentUsage } from "@/utils/usageAnalytics";
import UsageOverviewCards from "@/components/admin/UsageOverviewCards";
import UsageChartsPanel from "@/components/admin/UsageChartsPanel";
import RecentUsageTable from "@/components/admin/RecentUsageTable";
import TutorialsManagementTable from "@/components/admin/TutorialsManagementTable";
import { AlertTriangle, Shield } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import NavBar from "@/components/layouts/NavBar";
import TierSettingsPanel from "@/components/admin/TierSettingsPanel";

const AdminDashboard = () => {
  const { user, isLoaded, isAdmin } = useAuth();

  const { data: usageStats, isLoading: statsLoading, error: statsError } = useQuery({
    queryKey: ['admin-usage-stats'],
    queryFn: () => getUsageStats(),
    enabled: isAdmin,
  });

  const { data: recentUsage, isLoading: recentLoading, error: recentError } = useQuery({
    queryKey: ['admin-recent-usage'],
    queryFn: () => getRecentUsage(100000),
    enabled: isAdmin,
  });

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">Loading...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar />
        <div className="container mx-auto px-4 py-8">
          <Card className="border-yellow-200 bg-yellow-50">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                <CardTitle className="text-yellow-800">Authentication Required</CardTitle>
              </div>
              <CardDescription className="text-yellow-600">
                Please sign in to access the admin dashboard.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar />
        <div className="container mx-auto px-4 py-8">
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-red-600" />
                <CardTitle className="text-red-800">Access Denied</CardTitle>
              </div>
              <CardDescription className="text-red-600">
                You don't have permission to access the admin dashboard. Admin privileges are required.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  if (statsError || recentError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <NavBar />
        <div className="container mx-auto px-4 py-8">
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <CardTitle className="text-red-800">Error Loading Dashboard</CardTitle>
              </div>
              <CardDescription className="text-red-600">
                Failed to load usage analytics. Please try again later.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <NavBar />
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Monitor API usage, costs, and system performance</p>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="recent">Recent Activity</TabsTrigger>
            <TabsTrigger value="tutorials">Tutorials</TabsTrigger>
            <TabsTrigger value="tiers">Tier Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <UsageOverviewCards 
              stats={usageStats} 
              isLoading={statsLoading} 
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <UsageChartsPanel 
              stats={usageStats} 
              recentUsage={recentUsage}
              isLoading={statsLoading || recentLoading} 
            />
          </TabsContent>

          <TabsContent value="recent" className="space-y-6">
            <RecentUsageTable 
              usage={recentUsage} 
              isLoading={recentLoading} 
            />
          </TabsContent>

          <TabsContent value="tutorials" className="space-y-6">
            <TutorialsManagementTable />
          </TabsContent>

          <TabsContent value="tiers" className="space-y-6">
            <TierSettingsPanel />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
